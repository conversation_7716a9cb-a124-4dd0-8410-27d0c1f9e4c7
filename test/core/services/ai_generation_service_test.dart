import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dio/dio.dart';

import 'package:hera/core/services/ai_generation_service.dart';
import 'package:hera/core/config/app_config.dart';
import 'package:hera/core/services/environment_service.dart';

// 生成Mock类
@GenerateMocks([Dio])
import 'ai_generation_service_test.mocks.dart';

void main() {
  group('AIGenerationService', () {
    late AIGenerationService aiService;
    late MockDio mockDio;

    setUp(() {
      mockDio = MockDio();
      aiService = AIGenerationService();
      
      // 设置测试API密钥
      ApiConfig.setApiKey('test_api_key_12345');
    });

    tearDown(() {
      // 清理
      ApiConfig.setApiKey('');
    });

    group('generateWeddingPhotoWithDALLE', () {
      test('should return image URL when API call is successful', () async {
        // Arrange
        const testDescription = 'A beautiful wedding photo';
        const expectedImageUrl = 'https://example.com/generated-image.jpg';
        
        final mockResponse = Response(
          requestOptions: RequestOptions(path: '/images/generations'),
          statusCode: 200,
          data: {
            'data': [
              {'url': expectedImageUrl}
            ]
          },
        );

        when(mockDio.post(
          any,
          data: anyNamed('data'),
        )).thenAnswer((_) async => mockResponse);

        // Act
        final result = await aiService.generateWeddingPhotoWithDALLE(testDescription);

        // Assert
        expect(result, equals(expectedImageUrl));
        verify(mockDio.post(
          '/images/generations',
          data: anyNamed('data'),
        )).called(1);
      });

      test('should throw exception when API call fails', () async {
        // Arrange
        const testDescription = 'A beautiful wedding photo';
        
        when(mockDio.post(
          any,
          data: anyNamed('data'),
        )).thenThrow(DioException(
          requestOptions: RequestOptions(path: '/images/generations'),
          response: Response(
            requestOptions: RequestOptions(path: '/images/generations'),
            statusCode: 401,
          ),
        ));

        // Act & Assert
        expect(
          () => aiService.generateWeddingPhotoWithDALLE(testDescription),
          throwsA(isA<Exception>()),
        );
      });

      test('should throw exception when response data is invalid', () async {
        // Arrange
        const testDescription = 'A beautiful wedding photo';
        
        final mockResponse = Response(
          requestOptions: RequestOptions(path: '/images/generations'),
          statusCode: 200,
          data: {
            'data': [] // 空数据
          },
        );

        when(mockDio.post(
          any,
          data: anyNamed('data'),
        )).thenAnswer((_) async => mockResponse);

        // Act & Assert
        expect(
          () => aiService.generateWeddingPhotoWithDALLE(testDescription),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('generateWeddingPhotos', () {
      test('should return success result when generation is successful', () async {
        // Arrange
        final testImages = [File('test/fixtures/test_image.jpg')];
        
        // 这个测试需要实际的图片文件，在实际项目中应该使用测试资源
        // 这里我们跳过，因为需要复杂的Mock设置
      });

      test('should return failure result when no images provided', () async {
        // Arrange
        final emptyImages = <File>[];

        // Act
        final result = await aiService.generateWeddingPhotos(emptyImages);

        // Assert
        expect(result.success, isFalse);
        expect(result.message, contains('请至少选择一张图片'));
      });

      test('should return failure result when too many images provided', () async {
        // Arrange
        final tooManyImages = List.generate(6, (index) => File('test$index.jpg'));

        // Act
        final result = await aiService.generateWeddingPhotos(tooManyImages);

        // Assert
        expect(result.success, isFalse);
        expect(result.message, contains('最多支持5张图片'));
      });
    });

    group('API Key Validation', () {
      test('should throw exception when API key is not configured', () async {
        // Arrange
        ApiConfig.setApiKey(''); // 清空API密钥
        const testDescription = 'A beautiful wedding photo';

        // Act & Assert
        expect(
          () => aiService.generateWeddingPhotoWithDALLE(testDescription),
          throwsA(isA<Exception>()),
        );
      });
    });
  });

  group('EnvironmentService', () {
    group('validateApiKey', () {
      test('should return true for valid API key', () {
        // Arrange
        const validApiKey = 'sk-1234567890abcdef';

        // Act
        final result = EnvironmentService.validateApiKey(validApiKey);

        // Assert
        expect(result, isTrue);
      });

      test('should return false for empty API key', () {
        // Arrange
        const emptyApiKey = '';

        // Act
        final result = EnvironmentService.validateApiKey(emptyApiKey);

        // Assert
        expect(result, isFalse);
      });

      test('should return false for placeholder API key', () {
        // Arrange
        const placeholderApiKey = 'YOUR_API_KEY';

        // Act
        final result = EnvironmentService.validateApiKey(placeholderApiKey);

        // Assert
        expect(result, isFalse);
      });

      test('should return false for short API key', () {
        // Arrange
        const shortApiKey = '123';

        // Act
        final result = EnvironmentService.validateApiKey(shortApiKey);

        // Assert
        expect(result, isFalse);
      });
    });

    group('getConfigStatus', () {
      test('should return correct config status', () {
        // Arrange
        ApiConfig.setApiKey('test_api_key_12345');

        // Act
        final status = EnvironmentService.getConfigStatus();

        // Assert
        expect(status['hasApiKey'], isTrue);
        expect(status['apiKeyValid'], isTrue);
        expect(status['baseUrl'], equals(ApiConfig.aiGenerationBaseUrl));
      });
    });
  });
}
