import 'package:flutter/material.dart';
import 'wedding_colors.dart';

/// 婚礼主题字体配置
class WeddingTypography {
  // 字体家族
  static const String primaryFontFamily = 'SF Pro Display'; // iOS系统字体
  static const String scriptFontFamily = 'Dancing Script'; // 优雅的脚本字体
  static const String serifFontFamily = 'Playfair Display'; // 衬线字体用于标题
  
  /// 浅色主题文本样式
  static TextTheme get lightTextTheme => TextTheme(
    // 大标题 - 使用脚本字体
    displayLarge: TextStyle(
      fontFamily: scriptFontFamily,
      fontSize: 32,
      fontWeight: FontWeight.w400,
      color: WeddingColors.primaryPink,
      height: 1.2,
    ),
    displayMedium: TextStyle(
      fontFamily: scriptFontFamily,
      fontSize: 28,
      fontWeight: FontWeight.w400,
      color: WeddingColors.primaryPink,
      height: 1.2,
    ),
    displaySmall: TextStyle(
      fontFamily: scriptFontFamily,
      fontSize: 24,
      fontWeight: FontWeight.w400,
      color: WeddingColors.primaryPink,
      height: 1.2,
    ),
    
    // 标题 - 使用衬线字体
    headlineLarge: TextStyle(
      fontFamily: serifFontFamily,
      fontSize: 22,
      fontWeight: FontWeight.w600,
      color: WeddingColors.darkGray,
      height: 1.3,
    ),
    headlineMedium: TextStyle(
      fontFamily: serifFontFamily,
      fontSize: 20,
      fontWeight: FontWeight.w600,
      color: WeddingColors.darkGray,
      height: 1.3,
    ),
    headlineSmall: TextStyle(
      fontFamily: serifFontFamily,
      fontSize: 18,
      fontWeight: FontWeight.w600,
      color: WeddingColors.darkGray,
      height: 1.3,
    ),
    
    // 标题 - 使用系统字体
    titleLarge: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: WeddingColors.darkGray,
      height: 1.4,
    ),
    titleMedium: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w600,
      color: WeddingColors.darkGray,
      height: 1.4,
    ),
    titleSmall: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 12,
      fontWeight: FontWeight.w600,
      color: WeddingColors.darkGray,
      height: 1.4,
    ),
    
    // 正文
    bodyLarge: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 16,
      fontWeight: FontWeight.w400,
      color: WeddingColors.darkGray,
      height: 1.5,
    ),
    bodyMedium: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w400,
      color: WeddingColors.darkGray,
      height: 1.5,
    ),
    bodySmall: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 12,
      fontWeight: FontWeight.w400,
      color: WeddingColors.mediumGray, // 现在使用优化后的 mediumGray (#9E9E9E)
      height: 1.5,
    ),
    
    // 标签
    labelLarge: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w500,
      color: WeddingColors.darkGray,
      height: 1.4,
    ),
    labelMedium: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 12,
      fontWeight: FontWeight.w500,
      color: WeddingColors.darkGray,
      height: 1.4,
    ),
    labelSmall: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 10,
      fontWeight: FontWeight.w500,
      color: WeddingColors.mediumGray, // 现在使用优化后的 mediumGray (#9E9E9E)
      height: 1.4,
    ),
  );
  
  /// 深色主题文本样式
  static TextTheme get darkTextTheme => TextTheme(
    // 大标题 - 使用脚本字体
    displayLarge: TextStyle(
      fontFamily: scriptFontFamily,
      fontSize: 32,
      fontWeight: FontWeight.w400,
      color: WeddingColors.primaryPinkLight,
      height: 1.2,
    ),
    displayMedium: TextStyle(
      fontFamily: scriptFontFamily,
      fontSize: 28,
      fontWeight: FontWeight.w400,
      color: WeddingColors.primaryPinkLight,
      height: 1.2,
    ),
    displaySmall: TextStyle(
      fontFamily: scriptFontFamily,
      fontSize: 24,
      fontWeight: FontWeight.w400,
      color: WeddingColors.primaryPinkLight,
      height: 1.2,
    ),
    
    // 标题 - 使用衬线字体
    headlineLarge: TextStyle(
      fontFamily: serifFontFamily,
      fontSize: 22,
      fontWeight: FontWeight.w600,
      color: WeddingColors.pureWhite,
      height: 1.3,
    ),
    headlineMedium: TextStyle(
      fontFamily: serifFontFamily,
      fontSize: 20,
      fontWeight: FontWeight.w600,
      color: WeddingColors.pureWhite,
      height: 1.3,
    ),
    headlineSmall: TextStyle(
      fontFamily: serifFontFamily,
      fontSize: 18,
      fontWeight: FontWeight.w600,
      color: WeddingColors.pureWhite,
      height: 1.3,
    ),
    
    // 标题 - 使用系统字体
    titleLarge: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: WeddingColors.pureWhite,
      height: 1.4,
    ),
    titleMedium: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w600,
      color: WeddingColors.pureWhite,
      height: 1.4,
    ),
    titleSmall: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 12,
      fontWeight: FontWeight.w600,
      color: WeddingColors.pureWhite,
      height: 1.4,
    ),
    
    // 正文
    bodyLarge: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 16,
      fontWeight: FontWeight.w400,
      color: WeddingColors.pureWhite,
      height: 1.5,
    ),
    bodyMedium: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w400,
      color: WeddingColors.pureWhite,
      height: 1.5,
    ),
    bodySmall: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 12,
      fontWeight: FontWeight.w400,
      color: WeddingColors.lightGray,
      height: 1.5,
    ),
    
    // 标签
    labelLarge: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w500,
      color: WeddingColors.pureWhite,
      height: 1.4,
    ),
    labelMedium: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 12,
      fontWeight: FontWeight.w500,
      color: WeddingColors.pureWhite,
      height: 1.4,
    ),
    labelSmall: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 10,
      fontWeight: FontWeight.w500,
      color: WeddingColors.lightGray,
      height: 1.4,
    ),
  );
}
