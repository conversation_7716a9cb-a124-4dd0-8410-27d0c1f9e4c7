import 'package:flutter/material.dart';

/// 婚礼主题颜色配置
class WeddingColors {
  // 主色调 - 柔和粉色系
  static const Color primaryPink = Color(0xFFE8B4CB);
  static const Color primaryPinkLight = Color(0xFFF5D7E3);
  static const Color primaryPinkDark = Color(0xFFD1899F);
  
  // 辅助色 - 金色系
  static const Color accentGold = Color(0xFFD4AF37);
  static const Color accentGoldLight = Color(0xFFE6C866);
  static const Color accentGoldDark = Color(0xFFB8941F);
  
  // 中性色 - 白色和灰色系
  static const Color pureWhite = Color(0xFFFFFFFF);
  static const Color creamWhite = Color(0xFFFFFBF7);
  static const Color lightGray = Color(0xFFF8F8F8);
  static const Color mediumGray = Color(0xFF9E9E9E); // 优化：从 #E0E0E0 改为 #9E9E9E，提升可读性
  static const Color mediumLightGray = Color(0xFFBDBDBD); // 新增：介于浅灰和中灰之间的颜色
  static const Color darkGray = Color(0xFF424242); // 优化：从 #757575 改为 #424242，提升对比度
  
  // 深色模式专用色彩
  static const Color darkBackground = Color(0xFF1A1A1A);
  static const Color darkSurface = Color(0xFF2D2D2D);
  static const Color darkCard = Color(0xFF3A3A3A);
  
  // 功能性颜色
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // 渐变色
  static const LinearGradient pinkGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryPinkLight, primaryPink],
  );
  
  static const LinearGradient goldGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accentGoldLight, accentGold],
  );
  
  static const LinearGradient weddingGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryPinkLight, accentGoldLight],
  );
  
  // 阴影颜色
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);

  // 常用背景和文本颜色
  static const Color background = lightGray;
  static const Color surface = pureWhite;
  static const Color textPrimary = darkGray;
  static const Color textSecondary = Color(0xFF9E9E9E);
  
  /// 获取浅色主题的颜色方案
  static ColorScheme get lightColorScheme => ColorScheme.light(
    primary: primaryPink,
    primaryContainer: primaryPinkLight,
    secondary: accentGold,
    secondaryContainer: accentGoldLight,
    surface: pureWhite,
    background: lightGray,
    error: error,
    onPrimary: pureWhite,
    onSecondary: pureWhite,
    onSurface: darkGray,
    onBackground: darkGray,
    onError: pureWhite,
  );
  
  /// 获取深色主题的颜色方案
  static ColorScheme get darkColorScheme => ColorScheme.dark(
    primary: primaryPinkLight,
    primaryContainer: primaryPinkDark,
    secondary: accentGoldLight,
    secondaryContainer: accentGoldDark,
    surface: darkSurface,
    background: darkBackground,
    error: error,
    onPrimary: darkBackground,
    onSecondary: darkBackground,
    onSurface: pureWhite,
    onBackground: pureWhite,
    onError: pureWhite,
  );
}
