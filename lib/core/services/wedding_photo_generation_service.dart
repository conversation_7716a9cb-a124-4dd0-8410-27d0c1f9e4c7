import 'dart:io';
import 'package:flutter/foundation.dart';
import 'ai_generation_service.dart';
import 'portrait_detection_service.dart';
import '../config/secure_config.dart';
import '../config/ai_config.dart';

/// 婚纱照生成完整流程服务
/// 包含人像检测、图片分析和婚纱照生成的完整流程
class WeddingPhotoGenerationService {
  final AIGenerationService _aiService;
  final PortraitDetectionService _portraitDetection;

  WeddingPhotoGenerationService()
      : _aiService = AIGenerationService(),
        _portraitDetection = PortraitDetectionService();

  /// 完整的婚纱照生成流程
  /// 1. 验证配置
  /// 2. 检测人像
  /// 3. 分析图片
  /// 4. 生成婚纱照
  Future<WeddingPhotoResult> generateWeddingPhoto({
    required List<File> inputImages,
    String style = 'romantic',
    String size = '1024x1024',
    String quality = 'hd',
    bool generateMultipleStyles = false,
    bool skipPortraitDetection = false,
  }) async {
    final startTime = DateTime.now();
    
    try {
      debugPrint('开始婚纱照生成流程...');
      
      // 步骤1: 验证配置
      await _validateConfiguration();
      
      // 步骤2: 验证输入
      _validateInput(inputImages);
      
      // 步骤3: 人像检测（可选跳过）
      List<PortraitDetectionResult> portraitResults = [];
      if (!skipPortraitDetection) {
        debugPrint('开始人像检测...');
        portraitResults = await _portraitDetection.detectMultiplePortraits(inputImages);
        
        // 验证人像检测结果
        final validImages = _validatePortraitResults(portraitResults, inputImages);
        if (validImages.isEmpty) {
          throw WeddingPhotoException(
            '没有检测到适合的人像图片，请上传包含清晰人脸的照片',
            WeddingPhotoErrorType.noValidPortrait,
            details: {'portraitResults': portraitResults.map((r) => r.toJson()).toList()},
          );
        }
        
        // 使用验证通过的图片
        inputImages = validImages;
      }
      
      // 步骤4: 生成婚纱照
      debugPrint('开始生成婚纱照...');
      final generationResult = await _aiService.generateWeddingPhotos(
        inputImages,
        style: style,
        size: size,
        quality: quality,
        generateMultipleStyles: generateMultipleStyles,
      );
      
      if (!generationResult.success) {
        throw WeddingPhotoException(
          '婚纱照生成失败: ${generationResult.message}',
          WeddingPhotoErrorType.generationFailed,
          details: {'generationResult': generationResult.toJson()},
        );
      }
      
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      debugPrint('婚纱照生成完成，耗时: ${duration.inSeconds}秒');
      
      return WeddingPhotoResult(
        success: true,
        message: '婚纱照生成成功！',
        generatedImageUrls: generationResult.allImageUrls,
        analysis: generationResult.analysis,
        portraitDetectionResults: portraitResults,
        generationDetails: generationResult,
        processingTime: duration,
        inputImageCount: inputImages.length,
        style: style,
        size: size,
        quality: quality,
      );
      
    } catch (e) {
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      debugPrint('婚纱照生成失败: $e');
      
      if (e is WeddingPhotoException) {
        return WeddingPhotoResult(
          success: false,
          message: e.message,
          generatedImageUrls: [],
          analysis: '',
          portraitDetectionResults: [],
          generationDetails: null,
          processingTime: duration,
          inputImageCount: inputImages.length,
          style: style,
          size: size,
          quality: quality,
          error: e,
        );
      } else {
        return WeddingPhotoResult(
          success: false,
          message: '生成过程中发生未知错误: $e',
          generatedImageUrls: [],
          analysis: '',
          portraitDetectionResults: [],
          generationDetails: null,
          processingTime: duration,
          inputImageCount: inputImages.length,
          style: style,
          size: size,
          quality: quality,
          error: WeddingPhotoException(
            e.toString(),
            WeddingPhotoErrorType.unknown,
          ),
        );
      }
    }
  }

  /// 快速生成（单张图片，跳过人像检测）
  Future<WeddingPhotoResult> quickGenerate(File imageFile) async {
    return generateWeddingPhoto(
      inputImages: [imageFile],
      style: 'romantic',
      skipPortraitDetection: false, // 仍然进行人像检测以确保质量
    );
  }

  /// 高级生成（多风格，完整检测）
  Future<WeddingPhotoResult> premiumGenerate(List<File> imageFiles) async {
    return generateWeddingPhoto(
      inputImages: imageFiles,
      style: 'romantic',
      generateMultipleStyles: true,
      skipPortraitDetection: false,
    );
  }

  /// 仅进行人像检测
  Future<List<PortraitDetectionResult>> detectPortraitsOnly(List<File> imageFiles) async {
    await _validateConfiguration();
    _validateInput(imageFiles);
    return await _portraitDetection.detectMultiplePortraits(imageFiles);
  }

  /// 验证配置
  Future<void> _validateConfiguration() async {
    // 确保SecureConfig已初始化
    await SecureConfig.initialize();
    
    if (!SecureConfig.isApiKeyValid) {
      throw WeddingPhotoException(
        'API密钥未配置或无效，请检查配置',
        WeddingPhotoErrorType.configurationError,
        details: SecureConfig.validateConfig(),
      );
    }
  }

  /// 验证输入
  void _validateInput(List<File> imageFiles) {
    if (imageFiles.isEmpty) {
      throw WeddingPhotoException(
        '请至少选择一张图片',
        WeddingPhotoErrorType.invalidInput,
      );
    }

    if (!AIConfig.isValidImageCount(imageFiles.length)) {
      throw WeddingPhotoException(
        '最多只能处理${AIConfig.maxImageCount}张图片',
        WeddingPhotoErrorType.invalidInput,
      );
    }
  }

  /// 验证人像检测结果并返回有效图片
  List<File> _validatePortraitResults(
    List<PortraitDetectionResult> results,
    List<File> originalFiles,
  ) {
    final validFiles = <File>[];
    
    for (int i = 0; i < results.length && i < originalFiles.length; i++) {
      final result = results[i];
      if (result.isSuitableForWedding) {
        validFiles.add(originalFiles[i]);
      }
    }
    
    return validFiles;
  }

  /// 获取服务状态
  Future<Map<String, dynamic>> getServiceStatus() async {
    try {
      await _validateConfiguration();
      return {
        'status': 'ready',
        'configValid': true,
        'apiKeyValid': SecureConfig.isApiKeyValid,
        'maskedApiKey': SecureConfig.maskedApiKey,
        'maxImageCount': AIConfig.maxImageCount,
        'maxImageSize': '${AIConfig.maxSingleImageSize ~/ (1024 * 1024)}MB',
        'supportedFormats': AIConfig.supportedImageFormats,
        'availableStyles': AIConfig.weddingStyles.keys.toList(),
        'availableScenes': AIConfig.weddingScenes.keys.toList(),
      };
    } catch (e) {
      return {
        'status': 'error',
        'configValid': false,
        'error': e.toString(),
      };
    }
  }
}

/// 婚纱照生成结果
class WeddingPhotoResult {
  final bool success;
  final String message;
  final List<String> generatedImageUrls;
  final String analysis;
  final List<PortraitDetectionResult> portraitDetectionResults;
  final dynamic generationDetails; // GenerationResult
  final Duration processingTime;
  final int inputImageCount;
  final String style;
  final String size;
  final String quality;
  final WeddingPhotoException? error;

  WeddingPhotoResult({
    required this.success,
    required this.message,
    required this.generatedImageUrls,
    required this.analysis,
    required this.portraitDetectionResults,
    required this.generationDetails,
    required this.processingTime,
    required this.inputImageCount,
    required this.style,
    required this.size,
    required this.quality,
    this.error,
  });

  /// 是否有生成的图片
  bool get hasGeneratedImages => generatedImageUrls.isNotEmpty;

  /// 生成的图片数量
  int get generatedImageCount => generatedImageUrls.length;

  /// 是否检测到人像
  bool get hasValidPortraits => portraitDetectionResults.any((r) => r.isSuitableForWedding);

  /// 处理时间（秒）
  double get processingTimeInSeconds => processingTime.inMilliseconds / 1000.0;

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'generatedImageUrls': generatedImageUrls,
      'analysis': analysis,
      'portraitDetectionResults': portraitDetectionResults.map((r) => r.toJson()).toList(),
      'generationDetails': generationDetails?.toJson(),
      'processingTimeSeconds': processingTimeInSeconds,
      'inputImageCount': inputImageCount,
      'style': style,
      'size': size,
      'quality': quality,
      'error': error?.toJson(),
    };
  }
}

/// 婚纱照生成异常
class WeddingPhotoException implements Exception {
  final String message;
  final WeddingPhotoErrorType type;
  final Map<String, dynamic> details;

  const WeddingPhotoException(
    this.message,
    this.type, {
    this.details = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'type': type.toString(),
      'details': details,
    };
  }

  @override
  String toString() => 'WeddingPhotoException: $message';
}

/// 错误类型
enum WeddingPhotoErrorType {
  configurationError,
  invalidInput,
  noValidPortrait,
  generationFailed,
  networkError,
  unknown,
}
