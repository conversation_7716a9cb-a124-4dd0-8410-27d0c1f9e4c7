import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../config/secure_config.dart';
import '../config/ai_config.dart';

/// 人像检测服务
/// 用于验证上传的图片是否包含人像
class PortraitDetectionService {
  final Dio _dio;

  PortraitDetectionService() : _dio = Dio() {
    _dio.options.connectTimeout = AIConfig.connectTimeout;
    _dio.options.receiveTimeout = AIConfig.receiveTimeout;
    _dio.options.sendTimeout = AIConfig.sendTimeout;
    
    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: false, // 不记录图片数据
        responseBody: true,
        logPrint: (obj) => debugPrint('[PortraitDetection] $obj'),
      ));
    }
  }

  /// 检测图片是否包含人像
  Future<PortraitDetectionResult> detectPortrait(File imageFile) async {
    try {
      // 验证文件
      await _validateImageFile(imageFile);

      // 将图片转换为base64
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);
      
      final prompt = _buildPortraitDetectionPrompt();
      
      final requestData = {
        'model': AIConfig.defaultChatModel,
        'messages': [
          {
            'role': 'user',
            'content': [
              {
                'type': 'text',
                'text': prompt,
              },
              {
                'type': 'image_url',
                'image_url': {
                  'url': 'data:image/jpeg;base64,$base64Image',
                  'detail': 'low' // 使用低精度以节省token
                }
              }
            ]
          }
        ],
        'max_tokens': 200,
        'temperature': 0.1, // 低温度确保一致性
      };

      debugPrint('开始人像检测...');

      final response = await _dio.post(
        AIConfig.getApiUrl('/chat/completions'),
        data: requestData,
        options: Options(headers: SecureConfig.getAuthHeaders()),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['choices'] != null && data['choices'].isNotEmpty) {
          final content = data['choices'][0]['message']['content'] as String;
          return _parseDetectionResult(content);
        } else {
          throw Exception('API返回数据格式错误');
        }
      } else {
        final errorMessage = response.data?['error']?['message'] ?? '未知错误';
        throw Exception('API调用失败: ${response.statusCode} - $errorMessage');
      }
    } on DioException catch (e) {
      debugPrint('人像检测网络错误: ${e.message}');
      if (e.response != null) {
        final errorData = e.response!.data;
        final errorMessage = errorData?['error']?['message'] ?? '网络请求失败';
        throw Exception('网络错误: $errorMessage');
      } else {
        throw Exception('网络连接失败，请检查网络设置');
      }
    } catch (e) {
      debugPrint('人像检测错误: $e');
      throw Exception('人像检测失败: $e');
    }
  }

  /// 批量检测多张图片
  Future<List<PortraitDetectionResult>> detectMultiplePortraits(List<File> imageFiles) async {
    if (imageFiles.isEmpty) {
      throw Exception('请至少选择一张图片');
    }

    if (imageFiles.length > AIConfig.maxImageCount) {
      throw Exception('最多只能同时检测${AIConfig.maxImageCount}张图片');
    }

    final results = <PortraitDetectionResult>[];
    
    for (int i = 0; i < imageFiles.length; i++) {
      try {
        debugPrint('检测第${i + 1}张图片...');
        final result = await detectPortrait(imageFiles[i]);
        results.add(result);
        
        // 添加延迟避免API限制
        if (i < imageFiles.length - 1) {
          await Future.delayed(const Duration(milliseconds: 500));
        }
      } catch (e) {
        results.add(PortraitDetectionResult(
          hasPortrait: false,
          confidence: 0.0,
          message: '检测失败: $e',
          details: {},
          fileName: imageFiles[i].path.split('/').last,
        ));
      }
    }
    
    return results;
  }

  /// 验证图片文件
  Future<void> _validateImageFile(File imageFile) async {
    if (!await imageFile.exists()) {
      throw Exception('图片文件不存在');
    }

    final fileSize = await imageFile.length();
    if (!AIConfig.isValidImageSize(fileSize)) {
      throw Exception('图片文件过大，请选择小于${AIConfig.maxSingleImageSize ~/ (1024 * 1024)}MB的图片');
    }

    final extension = imageFile.path.split('.').last.toLowerCase();
    if (!AIConfig.isValidImageFormat(extension)) {
      throw Exception('不支持的图片格式: $extension');
    }
  }

  /// 构建人像检测提示词
  String _buildPortraitDetectionPrompt() {
    return '''
请分析这张图片是否包含人像（人脸或人物）。

分析要求：
1. 检测图片中是否有清晰可见的人脸
2. 评估人像的质量和清晰度
3. 判断是否适合用于婚纱照生成

请按以下JSON格式回答：
{
  "hasPortrait": true/false,
  "confidence": 0.0-1.0,
  "faceCount": 数字,
  "quality": "excellent/good/fair/poor",
  "suitableForWedding": true/false,
  "issues": ["问题列表"],
  "recommendations": ["建议列表"]
}

要求：
- hasPortrait: 是否检测到人像
- confidence: 检测置信度(0-1)
- faceCount: 检测到的人脸数量
- quality: 图片质量评估
- suitableForWedding: 是否适合婚纱照生成
- issues: 发现的问题（如模糊、光线不足等）
- recommendations: 改进建议

只返回JSON，不要其他文字。
''';
  }

  /// 解析检测结果
  PortraitDetectionResult _parseDetectionResult(String content) {
    try {
      // 清理内容，提取JSON部分
      String jsonContent = content.trim();
      if (jsonContent.startsWith('```json')) {
        jsonContent = jsonContent.substring(7);
      }
      if (jsonContent.endsWith('```')) {
        jsonContent = jsonContent.substring(0, jsonContent.length - 3);
      }
      jsonContent = jsonContent.trim();

      final Map<String, dynamic> data = jsonDecode(jsonContent);
      
      return PortraitDetectionResult(
        hasPortrait: data['hasPortrait'] ?? false,
        confidence: (data['confidence'] ?? 0.0).toDouble(),
        message: _generateMessage(data),
        details: data,
      );
    } catch (e) {
      debugPrint('解析检测结果失败: $e');
      debugPrint('原始内容: $content');
      
      // 如果JSON解析失败，尝试简单的文本分析
      final lowerContent = content.toLowerCase();
      final hasPortrait = lowerContent.contains('true') || 
                         lowerContent.contains('人像') || 
                         lowerContent.contains('人脸') ||
                         lowerContent.contains('face') ||
                         lowerContent.contains('portrait');
      
      return PortraitDetectionResult(
        hasPortrait: hasPortrait,
        confidence: hasPortrait ? 0.5 : 0.1,
        message: hasPortrait ? '检测到人像（解析异常）' : '未检测到人像（解析异常）',
        details: {'rawContent': content, 'parseError': e.toString()},
      );
    }
  }

  /// 生成检测消息
  String _generateMessage(Map<String, dynamic> data) {
    final hasPortrait = data['hasPortrait'] ?? false;
    final confidence = (data['confidence'] ?? 0.0).toDouble();
    final faceCount = data['faceCount'] ?? 0;
    final quality = data['quality'] ?? 'unknown';
    final suitableForWedding = data['suitableForWedding'] ?? false;

    if (!hasPortrait) {
      return '未检测到人像，请上传包含清晰人脸的照片';
    }

    if (confidence < 0.5) {
      return '人像检测置信度较低，建议使用更清晰的照片';
    }

    if (!suitableForWedding) {
      final issues = data['issues'] as List<dynamic>? ?? [];
      return '检测到人像但不适合婚纱照生成：${issues.join(', ')}';
    }

    String message = '检测到$faceCount个人像，质量：$quality';
    if (confidence >= 0.8) {
      message += '，非常适合生成婚纱照';
    } else if (confidence >= 0.6) {
      message += '，适合生成婚纱照';
    } else {
      message += '，可以生成婚纱照但建议使用更清晰的照片';
    }

    return message;
  }
}

/// 人像检测结果
class PortraitDetectionResult {
  final bool hasPortrait;
  final double confidence;
  final String message;
  final Map<String, dynamic> details;
  final String fileName;

  PortraitDetectionResult({
    required this.hasPortrait,
    required this.confidence,
    required this.message,
    required this.details,
    this.fileName = '',
  });

  /// 是否适合婚纱照生成
  bool get isSuitableForWedding => 
      hasPortrait && confidence >= 0.5 && (details['suitableForWedding'] ?? false);

  /// 检测到的人脸数量
  int get faceCount => details['faceCount'] ?? 0;

  /// 图片质量
  String get quality => details['quality'] ?? 'unknown';

  /// 发现的问题
  List<String> get issues => 
      (details['issues'] as List<dynamic>?)?.cast<String>() ?? [];

  /// 改进建议
  List<String> get recommendations => 
      (details['recommendations'] as List<dynamic>?)?.cast<String>() ?? [];

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'hasPortrait': hasPortrait,
      'confidence': confidence,
      'message': message,
      'details': details,
      'fileName': fileName,
    };
  }

  @override
  String toString() {
    return 'PortraitDetectionResult(hasPortrait: $hasPortrait, confidence: $confidence, message: $message)';
  }
}
