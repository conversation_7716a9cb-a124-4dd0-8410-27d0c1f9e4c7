import 'dart:io';
import 'package:flutter/foundation.dart';
import '../utils/log_service.dart';
import '../config/app_config.dart';

/// 环境变量服务
class EnvironmentService {
  static final LogService _logger = LogService();
  
  /// 初始化环境配置
  static Future<void> initialize() async {
    _logger.i('初始化环境配置...');
    
    try {
      // 尝试从环境变量获取API密钥
      String? apiKey = _getApiKeyFromEnvironment();
      
      if (apiKey != null && apiKey.isNotEmpty) {
        ApiConfig.setApiKey(apiKey);
        _logger.i('API密钥配置成功');
      } else {
        // 如果环境变量中没有，使用默认配置（开发环境）
        _logger.w('未找到环境变量中的API密钥，请确保已正确配置');
        
        // 在开发环境中，可以设置一个占位符
        if (kDebugMode) {
          _logger.w('开发模式：使用占位符API密钥，请在生产环境中配置真实密钥');
          ApiConfig.setApiKey('YOUR_API_KEY_PLACEHOLDER');
        }
      }
    } catch (e, stackTrace) {
      _logger.e('环境配置初始化失败', e, stackTrace);
      rethrow;
    }
  }
  
  /// 从环境变量获取API密钥
  static String? _getApiKeyFromEnvironment() {
    // 尝试多个可能的环境变量名
    final possibleKeys = [
      'HDGSB_API_KEY',
      'AI_GENERATION_API_KEY', 
      'OPENAI_API_KEY',
      'API_KEY',
    ];
    
    for (final key in possibleKeys) {
      final value = Platform.environment[key];
      if (value != null && value.isNotEmpty) {
        _logger.d('找到API密钥环境变量: $key');
        return value;
      }
    }
    
    _logger.w('未找到任何API密钥环境变量: ${possibleKeys.join(', ')}');
    return null;
  }
  
  /// 设置API密钥（用于运行时配置）
  static void setApiKey(String apiKey) {
    if (apiKey.isEmpty) {
      throw ArgumentError('API密钥不能为空');
    }
    
    ApiConfig.setApiKey(apiKey);
    _logger.i('API密钥已更新');
  }
  
  /// 验证API密钥格式
  static bool validateApiKey(String apiKey) {
    if (apiKey.isEmpty) {
      return false;
    }
    
    // 基本格式验证 - 可以根据实际API密钥格式调整
    if (apiKey.length < 10) {
      _logger.w('API密钥长度过短，可能无效');
      return false;
    }
    
    if (apiKey == 'YOUR_API_KEY_PLACEHOLDER' || 
        apiKey == 'YOUR_API_KEY' ||
        apiKey == 'your_api_key_here') {
      _logger.w('检测到占位符API密钥，请配置真实密钥');
      return false;
    }
    
    return true;
  }
  
  /// 获取当前API配置状态
  static Map<String, dynamic> getConfigStatus() {
    return {
      'hasApiKey': ApiConfig.hasApiKey,
      'apiKeyValid': ApiConfig.hasApiKey ? validateApiKey(ApiConfig.apiKey) : false,
      'baseUrl': ApiConfig.aiGenerationBaseUrl,
      'environment': AppConfig.environment.name,
    };
  }
  
  /// 清除API密钥（用于登出等场景）
  static void clearApiKey() {
    ApiConfig.setApiKey('');
    _logger.i('API密钥已清除');
  }
}
