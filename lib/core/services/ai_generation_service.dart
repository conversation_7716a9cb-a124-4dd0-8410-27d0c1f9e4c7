import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../config/app_config.dart';
import '../utils/log_service.dart';

/// AI图片生成服务
class AIGenerationService {
  final Dio _dio;
  final LogService _logger;

  AIGenerationService() :
    _dio = Dio(),
    _logger = LogService() {

    _logger.i('初始化AI图片生成服务');

    _dio.options = BaseOptions(
      baseUrl: ApiConfig.aiGenerationBaseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 60),
      sendTimeout: const Duration(seconds: 60),
    );

    // 添加请求拦截器来动态设置Authorization头
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          try {
            final apiKey = ApiConfig.apiKey;
            options.headers['Authorization'] = 'Bearer $apiKey';
            options.headers['Content-Type'] = 'application/json';
            _logger.d('API请求: ${options.method} ${options.path}');
          } catch (e) {
            _logger.e('设置API密钥失败: $e');
            handler.reject(DioException(
              requestOptions: options,
              error: 'API密钥未配置或无效',
              type: DioExceptionType.unknown,
            ));
            return;
          }
          handler.next(options);
        },
        onResponse: (response, handler) {
          _logger.d('API响应: ${response.statusCode} ${response.requestOptions.path}');
          handler.next(response);
        },
        onError: (error, handler) {
          _logger.e('API请求错误: ${error.message}', error.error);
          handler.next(error);
        },
      ),
    );
  }

  /// 分析图片并生成婚纱照描述
  Future<String> analyzeImageForWeddingPhoto(File imageFile) async {
    _logger.i('开始分析单张图片: ${imageFile.path}');

    try {
      // 检查文件是否存在
      if (!await imageFile.exists()) {
        throw Exception('图片文件不存在: ${imageFile.path}');
      }

      // 将图片转换为base64
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);
      _logger.d('图片转换为base64完成，大小: ${bytes.length} bytes');

      final prompt = _buildWeddingPhotoPrompt();

      final requestData = {
        'model': 'gpt-4o',
        'messages': [
          {
            'role': 'user',
            'content': [
              {
                'type': 'text',
                'text': prompt,
              },
              {
                'type': 'image_url',
                'image_url': {
                  'url': 'data:image/jpeg;base64,$base64Image',
                  'detail': 'high'
                }
              }
            ]
          }
        ],
        'max_tokens': 500,
        'temperature': 0.7,
      };

      _logger.d('发送图片分析请求...');
      final response = await _dio.post(
        '/chat/completions',
        data: requestData,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data != null && data['choices'] != null && data['choices'].isNotEmpty) {
          final content = data['choices'][0]['message']['content'] as String;
          _logger.i('图片分析完成，描述长度: ${content.length}');
          return content;
        } else {
          _logger.e('分析响应数据格式错误: $data');
          throw Exception('分析响应数据格式错误');
        }
      } else {
        _logger.e('图片分析API调用失败，状态码: ${response.statusCode}');
        throw Exception('API调用失败: ${response.statusCode}');
      }
    } on DioException catch (e) {
      _logger.e('图片分析API请求异常', e);
      if (e.response?.statusCode == 401) {
        throw Exception('API密钥无效，请检查配置');
      } else if (e.response?.statusCode == 413) {
        throw Exception('图片文件过大，请选择较小的图片');
      } else {
        throw Exception('网络请求失败: ${e.message}');
      }
    } catch (e, stackTrace) {
      _logger.e('图片分析异常', e, stackTrace);
      throw Exception('图片分析失败: $e');
    }
  }

  /// 分析多张图片并生成综合婚纱照描述
  Future<String> analyzeMultipleImagesForWeddingPhoto(List<File> imageFiles) async {
    _logger.i('开始分析多张图片，数量: ${imageFiles.length}');

    try {
      List<Map<String, dynamic>> imageContents = [];

      // 处理所有图片
      for (int i = 0; i < imageFiles.length; i++) {
        final file = imageFiles[i];
        _logger.d('处理第${i + 1}张图片: ${file.path}');

        if (!await file.exists()) {
          throw Exception('图片文件不存在: ${file.path}');
        }

        final bytes = await file.readAsBytes();
        final base64Image = base64Encode(bytes);
        _logger.d('第${i + 1}张图片转换完成，大小: ${bytes.length} bytes');

        imageContents.add({
          'type': 'image_url',
          'image_url': {
            'url': 'data:image/jpeg;base64,$base64Image',
            'detail': 'high'
          }
        });
      }

      final prompt = _buildMultipleImagesWeddingPrompt(imageFiles.length);

      final requestData = {
        'model': 'gpt-4o',
        'messages': [
          {
            'role': 'user',
            'content': [
              {
                'type': 'text',
                'text': prompt,
              },
              ...imageContents,
            ]
          }
        ],
        'max_tokens': 800,
        'temperature': 0.7,
      };

      _logger.d('发送多图片分析请求...');
      final response = await _dio.post(
        '/chat/completions',
        data: requestData,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data != null && data['choices'] != null && data['choices'].isNotEmpty) {
          final content = data['choices'][0]['message']['content'] as String;
          _logger.i('多图片分析完成，描述长度: ${content.length}');
          return content;
        } else {
          _logger.e('多图片分析响应数据格式错误: $data');
          throw Exception('分析响应数据格式错误');
        }
      } else {
        _logger.e('多图片分析API调用失败，状态码: ${response.statusCode}');
        throw Exception('API调用失败: ${response.statusCode}');
      }
    } on DioException catch (e) {
      _logger.e('多图片分析API请求异常', e);
      if (e.response?.statusCode == 401) {
        throw Exception('API密钥无效，请检查配置');
      } else if (e.response?.statusCode == 413) {
        throw Exception('图片文件总大小过大，请减少图片数量或选择较小的图片');
      } else {
        throw Exception('网络请求失败: ${e.message}');
      }
    } catch (e, stackTrace) {
      _logger.e('多图片分析异常', e, stackTrace);
      throw Exception('图片分析失败: $e');
    }
  }

  /// 使用DALL-E生成婚纱照
  Future<String> generateWeddingPhotoWithDALLE(String description) async {
    _logger.i('开始使用DALL-E生成婚纱照');
    _logger.d('生成描述: $description');

    try {
      final enhancedPrompt = _enhancePromptForDALLE(description);
      _logger.d('增强后的提示词长度: ${enhancedPrompt.length}');

      final requestData = {
        'model': 'dall-e-3',
        'prompt': enhancedPrompt,
        'n': 1,
        'size': '1024x1024',
        'quality': 'hd',
        'style': 'vivid',
        'response_format': 'url',
      };

      _logger.d('发送DALL-E生成请求...');
      final response = await _dio.post(
        '/images/generations',
        data: requestData,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data != null && data['data'] != null && data['data'].isNotEmpty) {
          final imageUrl = data['data'][0]['url'] as String;
          _logger.i('DALL-E生成成功，图片URL: ${imageUrl.substring(0, 50)}...');
          return imageUrl;
        } else {
          _logger.e('DALL-E响应数据格式错误: $data');
          throw Exception('生成响应数据格式错误');
        }
      } else {
        _logger.e('DALL-E生成失败，状态码: ${response.statusCode}');
        throw Exception('DALL-E生成失败: ${response.statusCode}');
      }
    } on DioException catch (e) {
      _logger.e('DALL-E API请求异常', e);
      if (e.response?.statusCode == 401) {
        throw Exception('API密钥无效，请检查配置');
      } else if (e.response?.statusCode == 429) {
        throw Exception('API请求频率过高，请稍后重试');
      } else if (e.response?.statusCode == 400) {
        throw Exception('请求参数错误，请检查输入内容');
      } else {
        throw Exception('网络请求失败: ${e.message}');
      }
    } catch (e, stackTrace) {
      _logger.e('DALL-E生成异常', e, stackTrace);
      throw Exception('图片生成失败: $e');
    }
  }

  /// 构建单张图片婚纱照分析提示词
  String _buildWeddingPhotoPrompt() {
    return '''
请仔细分析这张照片中的人物特征，并为AI生成婚纱照提供详细描述。请关注以下要点：

1. **人物特征**：
   - 面部特征（脸型、眼睛、鼻子、嘴唇等）
   - 肤色和肤质
   - 发型和发色
   - 身材比例

2. **婚纱照风格建议**：
   - 适合的婚纱款式（如A字裙、鱼尾裙、公主裙等）
   - 拍摄场景建议（如教堂、花园、海边、室内等）
   - 灯光和氛围建议
   - pose和表情建议

3. **色彩搭配**：
   - 主色调建议
   - 配饰颜色搭配
   - 背景色彩建议

请用中文回答，并提供一个详细的、适合用于AI图片生成的描述，重点突出婚纱照的浪漫、优雅和梦幻感。
''';
  }

  /// 构建多张图片婚纱照分析提示词
  String _buildMultipleImagesWeddingPrompt(int imageCount) {
    return '''
请分析这${imageCount}张照片中的人物特征，并为AI生成婚纱照提供综合性的详细描述。

**分析要求**：
1. **综合人物特征**：
   - 从多个角度分析面部特征的一致性
   - 确定最佳的面部角度和表情
   - 分析身材比例和姿态
   - 发型发色的最佳展现方式

2. **婚纱照创意方案**：
   - 基于人物气质选择最适合的婚纱风格
   - 推荐3-5种不同的拍摄场景和主题
   - 灯光布置和拍摄角度建议
   - 整体构图和氛围营造

3. **风格定位**：
   - 经典优雅风格
   - 现代时尚风格  
   - 浪漫梦幻风格
   - 自然清新风格

4. **技术细节**：
   - 色彩搭配方案
   - 妆容建议
   - 配饰选择
   - 后期处理建议

请用中文提供一个完整的、专业的婚纱照AI生成描述，确保生成的婚纱照能够展现出最美的效果。
''';
  }

  /// 增强DALL-E生成提示词
  String _enhancePromptForDALLE(String originalDescription) {
    final basePrompt = '''
High-quality professional wedding photography, beautiful bride in elegant wedding dress, 
studio lighting, romantic atmosphere, dreamy background, soft focus, 
professional photographer style, wedding magazine quality, 8K resolution, photorealistic.

Based on the analysis: $originalDescription

Key requirements:
- Professional wedding photography style
- Elegant and romantic atmosphere  
- High-quality lighting and composition
- Beautiful wedding dress details
- Dreamy and enchanting mood
- Magazine-quality aesthetic
''';
    
    return basePrompt;
  }

  /// 生成婚纱照的完整流程
  Future<GenerationResult> generateWeddingPhotos(List<File> images) async {
    _logger.i('开始婚纱照生成流程，图片数量: ${images.length}');

    try {
      // 验证输入
      if (images.isEmpty) {
        throw Exception('请至少选择一张图片');
      }

      if (images.length > 5) {
        throw Exception('最多支持5张图片，当前选择了${images.length}张');
      }

      // 验证API密钥
      if (!ApiConfig.hasApiKey) {
        throw Exception('API密钥未配置，请联系管理员');
      }

      _logger.d('输入验证通过，开始处理...');

      // 步骤1: 分析图片
      _logger.i('步骤1: 开始图片分析...');
      String analysis;
      if (images.length == 1) {
        analysis = await analyzeImageForWeddingPhoto(images.first);
      } else {
        analysis = await analyzeMultipleImagesForWeddingPhoto(images);
      }
      _logger.i('图片分析完成');

      // 步骤2: 生成图片
      _logger.i('步骤2: 开始AI图片生成...');
      final generatedImageUrl = await generateWeddingPhotoWithDALLE(analysis);
      _logger.i('AI图片生成完成');

      final result = GenerationResult(
        success: true,
        analysis: analysis,
        generatedImageUrl: generatedImageUrl,
        message: '婚纱照生成成功！',
      );

      _logger.i('婚纱照生成流程完成');
      return result;

    } catch (e, stackTrace) {
      _logger.e('婚纱照生成流程失败', e, stackTrace);
      return GenerationResult(
        success: false,
        analysis: '',
        generatedImageUrl: '',
        message: '生成失败: $e',
      );
    }
  }
}

/// 生成结果数据类
class GenerationResult {
  final bool success;
  final String analysis;
  final String generatedImageUrl;
  final String message;

  GenerationResult({
    required this.success,
    required this.analysis,
    required this.generatedImageUrl,
    required this.message,
  });
} 