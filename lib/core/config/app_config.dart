import 'package:flutter/foundation.dart';

/// 应用环境类型
enum Environment {
  /// 开发环境
  dev,

  /// 测试环境
  test,

  /// 生产环境
  prod,
}

/// API配置类
class ApiConfig {
  /// AI图片生成API配置
  static const String aiGenerationBaseUrl = 'https://api.hdgsb.com/v1';

  /// API密钥 - 从环境变量获取
  static String? _apiKey;

  /// 设置API密钥
  static void setApiKey(String apiKey) {
    _apiKey = apiKey;
  }

  /// 获取API密钥
  static String get apiKey {
    if (_apiKey == null || _apiKey!.isEmpty) {
      throw Exception('API密钥未配置，请在应用启动时设置API密钥');
    }
    return _apiKey!;
  }

  /// 检查API密钥是否已配置
  static bool get hasApiKey => _apiKey != null && _apiKey!.isNotEmpty;
}

/// 应用程序配置
class AppConfig {
  /// 当前环境
  static Environment environment = Environment.dev;
  
  /// API基础URL
  static String get apiBaseUrl {
    switch (environment) {
      case Environment.dev:
        return 'https://dev-api.hera-wedding.com/v1';
      case Environment.test:
        return 'https://test-api.hera-wedding.com/v1';
      case Environment.prod:
        return 'https://api.hera-wedding.com/v1';
    }
  }

  /// AI图片生成API URL
  static String get aiApiBaseUrl {
    switch (environment) {
      case Environment.dev:
        return 'https://dev-ai.hera-wedding.com/v1';
      case Environment.test:
        return 'https://test-ai.hera-wedding.com/v1';
      case Environment.prod:
        return 'https://ai.hera-wedding.com/v1';
    }
  }

  /// 应用名称
  static String get appName {
    switch (environment) {
      case Environment.dev:
        return 'Hera AI婚纱照 (Dev)';
      case Environment.test:
        return 'Hera AI婚纱照 (Test)';
      case Environment.prod:
        return 'Hera AI婚纱照';
    }
  }
  
  /// 是否启用日志
  static bool get enableLogging {
    return environment != Environment.prod || kDebugMode;
  }
  
  /// 设置环境
  static void setEnvironment(Environment env) {
    environment = env;
  }
  
  /// 缓存配置
  static Duration get cacheMaxAge => const Duration(days: 7);

  /// 缓存清理频率
  static Duration get cacheClearInterval => const Duration(days: 1);

  /// AI生成相关配置
  static const int maxImageUploadSize = 10 * 1024 * 1024; // 10MB
  static const int maxBatchUploadCount = 5; // 最大批量上传数量
  static const Duration aiGenerationTimeout = Duration(minutes: 5); // AI生成超时时间

  /// 支持的图片格式
  static const List<String> supportedImageFormats = [
    'jpg', 'jpeg', 'png', 'webp'
  ];

  /// 婚纱照风格配置
  static const List<String> weddingStyles = [
    'chinese_traditional', // 中式传统
    'western_classic',     // 西式经典
    'vintage_retro',       // 复古怀旧
    'modern_minimalist',   // 现代简约
    'fantasy_romantic',    // 奇幻浪漫
  ];

  /// 免费用户限制
  static const int freeUserDailyLimit = 3; // 免费用户每日生成限制
  static const int freeUserMonthlyLimit = 20; // 免费用户每月生成限制

  /// 高级用户限制
  static const int premiumUserDailyLimit = 50; // 高级用户每日生成限制
  static const int premiumUserMonthlyLimit = 500; // 高级用户每月生成限制
}