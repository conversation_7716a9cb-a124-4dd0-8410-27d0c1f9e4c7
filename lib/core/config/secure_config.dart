import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// 安全配置管理类
/// 处理API密钥等敏感信息，避免硬编码
class SecureConfig {
  static String? _apiKey;
  static bool _initialized = false;

  /// 初始化配置
  static Future<void> initialize() async {
    if (_initialized) return;

    try {
      // 1. 优先从环境变量获取
      _apiKey = Platform.environment['LAOZHANG_AI_API_KEY'];
      
      // 2. 如果环境变量没有，尝试从assets获取
      if (_apiKey == null || _apiKey!.isEmpty) {
        try {
          final configString = await rootBundle.loadString('assets/config/api_config.txt');
          final lines = configString.split('\n');
          for (final line in lines) {
            if (line.startsWith('LAOZHANG_AI_API_KEY=')) {
              _apiKey = line.split('=')[1].trim();
              break;
            }
          }
        } catch (e) {
          debugPrint('无法从assets加载API配置: $e');
        }
      }

      // 3. 如果还是没有，使用默认的占位符（开发环境）
      if (_apiKey == null || _apiKey!.isEmpty) {
        if (kDebugMode) {
          _apiKey = 'DEBUG_MODE_PLACEHOLDER';
          debugPrint('警告: 使用调试模式占位符API密钥');
        }
      }

      _initialized = true;
      debugPrint('SecureConfig 初始化完成');
    } catch (e) {
      debugPrint('SecureConfig 初始化失败: $e');
      _apiKey = '';
    }
  }

  /// 获取API密钥
  static String get apiKey {
    if (!_initialized) {
      throw Exception('SecureConfig 未初始化，请先调用 initialize()');
    }
    return _apiKey ?? '';
  }

  /// 检查API密钥是否有效
  static bool get isApiKeyValid {
    final key = apiKey;
    return key.isNotEmpty && 
           key != 'YOUR_API_KEY' && 
           key != 'DEBUG_MODE_PLACEHOLDER';
  }

  /// 获取脱敏的API密钥（用于日志）
  static String get maskedApiKey {
    final key = apiKey;
    if (key.isEmpty) return '未设置';
    if (key.length <= 8) return '***';
    return '${key.substring(0, 4)}***${key.substring(key.length - 4)}';
  }

  /// 设置API密钥（仅用于测试或动态配置）
  static void setApiKey(String key) {
    _apiKey = key;
    debugPrint('API密钥已更新: $maskedApiKey');
  }

  /// 清除API密钥
  static void clearApiKey() {
    _apiKey = '';
    debugPrint('API密钥已清除');
  }

  /// 验证配置完整性
  static Map<String, dynamic> validateConfig() {
    return {
      'initialized': _initialized,
      'hasApiKey': _apiKey != null && _apiKey!.isNotEmpty,
      'isValid': isApiKeyValid,
      'maskedKey': maskedApiKey,
      'source': _getApiKeySource(),
    };
  }

  /// 获取API密钥来源
  static String _getApiKeySource() {
    if (_apiKey == null || _apiKey!.isEmpty) return '未设置';
    if (_apiKey == 'DEBUG_MODE_PLACEHOLDER') return '调试模式';
    if (Platform.environment['LAOZHANG_AI_API_KEY'] != null) return '环境变量';
    return 'assets配置文件';
  }

  /// 获取完整的请求头
  static Map<String, String> getAuthHeaders() {
    if (!isApiKeyValid) {
      throw Exception('API密钥无效，请检查配置');
    }
    
    return {
      'Authorization': 'Bearer $apiKey',
      'Content-Type': 'application/json',
      'User-Agent': 'HeraWeddingAI/1.0',
    };
  }

  /// 安全日志记录（不包含敏感信息）
  static void logSecurely(String message) {
    if (kDebugMode) {
      debugPrint('[SecureConfig] $message');
    }
  }
}

/// API密钥配置异常
class ApiKeyException implements Exception {
  final String message;
  const ApiKeyException(this.message);
  
  @override
  String toString() => 'ApiKeyException: $message';
}

/// 配置验证结果
class ConfigValidationResult {
  final bool isValid;
  final String message;
  final Map<String, dynamic> details;

  const ConfigValidationResult({
    required this.isValid,
    required this.message,
    required this.details,
  });

  factory ConfigValidationResult.success() {
    return ConfigValidationResult(
      isValid: true,
      message: '配置验证成功',
      details: SecureConfig.validateConfig(),
    );
  }

  factory ConfigValidationResult.failure(String reason) {
    return ConfigValidationResult(
      isValid: false,
      message: '配置验证失败: $reason',
      details: SecureConfig.validateConfig(),
    );
  }
}
