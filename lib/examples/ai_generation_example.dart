import 'dart:io';
import 'package:flutter/material.dart';
import '../core/services/ai_generation_service.dart';
import '../core/config/ai_config.dart';

/// AI生成服务使用示例
class AIGenerationExample {
  final AIGenerationService _aiService = AIGenerationService();

  /// 示例1: 单张图片快速生成婚纱照
  Future<void> quickGenerateExample() async {
    try {
      // 假设有一个图片文件
      final imageFile = File('path/to/your/image.jpg');
      
      print('开始快速生成婚纱照...');
      final result = await _aiService.quickGenerateWeddingPhoto(imageFile);
      
      if (result.success) {
        print('生成成功！');
        print('分析结果: ${result.analysis}');
        print('生成的图片URL: ${result.generatedImageUrl}');
        print('生成时间: ${result.generationTime}');
        print('使用风格: ${result.style}');
      } else {
        print('生成失败: ${result.message}');
      }
    } catch (e) {
      print('发生错误: $e');
    }
  }

  /// 示例2: 多张图片高级生成（多风格）
  Future<void> premiumGenerateExample() async {
    try {
      // 假设有多个图片文件
      final imageFiles = [
        File('path/to/image1.jpg'),
        File('path/to/image2.jpg'),
        File('path/to/image3.jpg'),
      ];
      
      print('开始高级生成婚纱照（多风格）...');
      final result = await _aiService.premiumGenerateWeddingPhotos(imageFiles);
      
      if (result.success) {
        print('生成成功！');
        print('分析结果: ${result.analysis}');
        print('主要图片URL: ${result.generatedImageUrl}');
        
        if (result.hasMultipleImages) {
          print('额外生成的图片数量: ${result.additionalImageUrls.length}');
          for (int i = 0; i < result.additionalImageUrls.length; i++) {
            print('额外图片${i + 1}: ${result.additionalImageUrls[i]}');
          }
        }
        
        print('总共生成图片数量: ${result.imageCount}');
      } else {
        print('生成失败: ${result.message}');
      }
    } catch (e) {
      print('发生错误: $e');
    }
  }

  /// 示例3: 自定义参数生成
  Future<void> customGenerateExample() async {
    try {
      final imageFiles = [File('path/to/image.jpg')];
      
      print('开始自定义参数生成...');
      final result = await _aiService.generateWeddingPhotos(
        imageFiles,
        style: 'natural', // 自然风格
        size: '1024x1024', // 高清尺寸
        quality: 'hd', // 高质量
        generateMultipleStyles: false, // 不生成多风格
      );
      
      if (result.success) {
        print('自定义生成成功！');
        print('使用参数: 风格=${result.style}, 尺寸=${result.size}, 质量=${result.quality}');
        print('生成的图片URL: ${result.generatedImageUrl}');
      } else {
        print('生成失败: ${result.message}');
      }
    } catch (e) {
      print('发生错误: $e');
    }
  }

  /// 示例4: 仅分析图片（不生成）
  Future<void> analyzeOnlyExample() async {
    try {
      final imageFile = File('path/to/image.jpg');
      
      print('开始分析图片...');
      final analysis = await _aiService.analyzeImageForWeddingPhoto(imageFile);
      
      print('分析完成！');
      print('分析结果: $analysis');
    } catch (e) {
      print('分析失败: $e');
    }
  }

  /// 示例5: 多张图片分析
  Future<void> multipleImagesAnalyzeExample() async {
    try {
      final imageFiles = [
        File('path/to/image1.jpg'),
        File('path/to/image2.jpg'),
      ];
      
      print('开始分析多张图片...');
      final analysis = await _aiService.analyzeMultipleImagesForWeddingPhoto(imageFiles);
      
      print('多图片分析完成！');
      print('综合分析结果: $analysis');
    } catch (e) {
      print('分析失败: $e');
    }
  }

  /// 示例6: 生成多种风格的婚纱照
  Future<void> multipleStylesExample() async {
    try {
      final description = '''
      美丽的新娘，拥有优雅的面部轮廓和温柔的气质。
      适合拍摄浪漫风格的婚纱照，建议使用柔和的灯光和梦幻的背景。
      ''';
      
      print('开始生成多种风格的婚纱照...');
      final imageUrls = await _aiService.generateMultipleStyleWeddingPhotos(
        description,
        styles: ['vivid', 'natural'],
        size: '1024x1024',
        quality: 'hd',
      );
      
      print('多风格生成完成！');
      for (int i = 0; i < imageUrls.length; i++) {
        print('风格${i + 1}图片URL: ${imageUrls[i]}');
      }
    } catch (e) {
      print('生成失败: $e');
    }
  }

  /// 示例7: 检查配置和调试信息
  void checkConfigExample() {
    print('=== AI配置信息 ===');
    print('API基础URL: ${AIConfig.baseUrl}');
    print('API密钥已设置: ${AIConfig.isApiKeyValid}');
    print('默认聊天模型: ${AIConfig.defaultChatModel}');
    print('默认图片模型: ${AIConfig.defaultImageModel}');
    print('最大图片大小: ${AIConfig.maxSingleImageSize ~/ (1024 * 1024)}MB');
    print('最大图片数量: ${AIConfig.maxImageCount}');
    print('支持的图片格式: ${AIConfig.supportedImageFormats}');
    
    print('\n=== 婚纱照风格预设 ===');
    AIConfig.weddingStyles.forEach((key, value) {
      print('$key: ${value['name']} - ${value['description']}');
    });
    
    print('\n=== 场景预设 ===');
    AIConfig.weddingScenes.forEach((key, value) {
      print('$key: ${value['name']} - ${value['description']}');
    });
    
    print('\n=== 调试信息 ===');
    final debugInfo = AIConfig.getDebugInfo();
    debugInfo.forEach((key, value) {
      print('$key: $value');
    });
  }

  /// 示例8: 验证图片文件
  Future<void> validateImageExample() async {
    final imageFile = File('path/to/image.jpg');
    
    try {
      // 检查文件是否存在
      if (!await imageFile.exists()) {
        print('错误: 图片文件不存在');
        return;
      }
      
      // 检查文件大小
      final fileSize = await imageFile.length();
      if (!AIConfig.isValidImageSize(fileSize)) {
        print('错误: 图片文件过大 (${fileSize ~/ (1024 * 1024)}MB)');
        return;
      }
      
      // 检查文件格式
      final extension = imageFile.path.split('.').last;
      if (!AIConfig.isValidImageFormat(extension)) {
        print('错误: 不支持的图片格式 ($extension)');
        return;
      }
      
      print('图片验证通过！');
      print('文件大小: ${fileSize ~/ 1024}KB');
      print('文件格式: $extension');
    } catch (e) {
      print('验证失败: $e');
    }
  }

  /// 运行所有示例
  Future<void> runAllExamples() async {
    print('=== AI生成服务示例演示 ===\n');
    
    // 首先检查配置
    checkConfigExample();
    
    if (!AIConfig.isApiKeyValid) {
      print('\n警告: API密钥未设置，请在AIConfig中设置正确的API密钥');
      return;
    }
    
    print('\n开始运行生成示例...\n');
    
    // 注意：以下示例需要真实的图片文件路径
    // await quickGenerateExample();
    // await premiumGenerateExample();
    // await customGenerateExample();
    // await analyzeOnlyExample();
    // await multipleImagesAnalyzeExample();
    // await multipleStylesExample();
    // await validateImageExample();
    
    print('示例演示完成！');
  }
}

/// Flutter Widget示例
class AIGenerationExampleWidget extends StatefulWidget {
  const AIGenerationExampleWidget({Key? key}) : super(key: key);

  @override
  State<AIGenerationExampleWidget> createState() => _AIGenerationExampleWidgetState();
}

class _AIGenerationExampleWidgetState extends State<AIGenerationExampleWidget> {
  final AIGenerationExample _example = AIGenerationExample();
  bool _isLoading = false;
  String _result = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI生成服务示例'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ElevatedButton(
              onPressed: _isLoading ? null : _checkConfig,
              child: const Text('检查配置'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading ? null : _runExample,
              child: _isLoading 
                  ? const CircularProgressIndicator()
                  : const Text('运行示例'),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: SingleChildScrollView(
                child: Text(
                  _result,
                  style: const TextStyle(fontFamily: 'monospace'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _checkConfig() {
    setState(() {
      _result = '';
    });
    
    // 重定向print输出到result
    final buffer = StringBuffer();
    void printToBuffer(String text) {
      buffer.writeln(text);
    }
    
    // 这里需要修改example中的print方法来支持自定义输出
    _example.checkConfigExample();
    
    setState(() {
      _result = '配置检查完成，请查看控制台输出';
    });
  }

  Future<void> _runExample() async {
    setState(() {
      _isLoading = true;
      _result = '正在运行示例...';
    });

    try {
      await _example.runAllExamples();
      setState(() {
        _result = '示例运行完成，请查看控制台输出';
      });
    } catch (e) {
      setState(() {
        _result = '运行失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
