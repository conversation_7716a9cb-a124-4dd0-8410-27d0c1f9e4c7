import 'dart:io';
import 'package:flutter/material.dart';
import '../core/services/wedding_photo_generation_service.dart';
import '../core/config/secure_config.dart';

/// 婚纱照生成完整流程示例
class WeddingPhotoExample {
  final WeddingPhotoGenerationService _service = WeddingPhotoGenerationService();

  /// 示例1: 完整的婚纱照生成流程
  Future<void> completeWorkflowExample() async {
    try {
      // 初始化配置
      await SecureConfig.initialize();
      
      // 检查服务状态
      final status = await _service.getServiceStatus();
      print('服务状态: ${status['status']}');
      
      if (status['status'] != 'ready') {
        print('服务未就绪: ${status['error']}');
        return;
      }
      
      // 准备输入图片（实际使用时替换为真实路径）
      final inputImages = [
        File('path/to/portrait1.jpg'),
        File('path/to/portrait2.jpg'),
      ];
      
      print('开始完整的婚纱照生成流程...');
      
      // 执行完整流程
      final result = await _service.generateWeddingPhoto(
        inputImages: inputImages,
        style: 'romantic',
        size: '1024x1024',
        quality: 'hd',
        generateMultipleStyles: false,
      );
      
      // 处理结果
      if (result.success) {
        print('✅ 婚纱照生成成功！');
        print('处理时间: ${result.processingTimeInSeconds.toStringAsFixed(1)}秒');
        print('输入图片数量: ${result.inputImageCount}');
        print('生成图片数量: ${result.generatedImageCount}');
        
        // 人像检测结果
        print('\n📸 人像检测结果:');
        for (int i = 0; i < result.portraitDetectionResults.length; i++) {
          final detection = result.portraitDetectionResults[i];
          print('图片${i + 1}: ${detection.message}');
          print('  - 置信度: ${(detection.confidence * 100).toStringAsFixed(1)}%');
          print('  - 人脸数量: ${detection.faceCount}');
          print('  - 图片质量: ${detection.quality}');
        }
        
        // 生成的图片
        print('\n🎨 生成的婚纱照:');
        for (int i = 0; i < result.generatedImageUrls.length; i++) {
          print('图片${i + 1}: ${result.generatedImageUrls[i]}');
        }
        
        // AI分析结果
        print('\n🤖 AI分析结果:');
        print(result.analysis);
        
      } else {
        print('❌ 婚纱照生成失败: ${result.message}');
        if (result.error != null) {
          print('错误类型: ${result.error!.type}');
          print('错误详情: ${result.error!.details}');
        }
      }
      
    } catch (e) {
      print('❌ 发生异常: $e');
    }
  }

  /// 示例2: 快速生成（单张图片）
  Future<void> quickGenerateExample() async {
    try {
      await SecureConfig.initialize();
      
      final imageFile = File('path/to/portrait.jpg');
      
      print('开始快速生成婚纱照...');
      final result = await _service.quickGenerate(imageFile);
      
      if (result.success) {
        print('✅ 快速生成成功！');
        print('生成图片: ${result.generatedImageUrls.first}');
        print('处理时间: ${result.processingTimeInSeconds.toStringAsFixed(1)}秒');
      } else {
        print('❌ 快速生成失败: ${result.message}');
      }
    } catch (e) {
      print('❌ 快速生成异常: $e');
    }
  }

  /// 示例3: 高级生成（多风格）
  Future<void> premiumGenerateExample() async {
    try {
      await SecureConfig.initialize();
      
      final imageFiles = [
        File('path/to/portrait1.jpg'),
        File('path/to/portrait2.jpg'),
      ];
      
      print('开始高级生成婚纱照（多风格）...');
      final result = await _service.premiumGenerate(imageFiles);
      
      if (result.success) {
        print('✅ 高级生成成功！');
        print('生成了${result.generatedImageCount}张不同风格的婚纱照');
        
        for (int i = 0; i < result.generatedImageUrls.length; i++) {
          print('风格${i + 1}: ${result.generatedImageUrls[i]}');
        }
      } else {
        print('❌ 高级生成失败: ${result.message}');
      }
    } catch (e) {
      print('❌ 高级生成异常: $e');
    }
  }

  /// 示例4: 仅进行人像检测
  Future<void> portraitDetectionOnlyExample() async {
    try {
      await SecureConfig.initialize();
      
      final imageFiles = [
        File('path/to/image1.jpg'),
        File('path/to/image2.jpg'),
        File('path/to/not_portrait.jpg'), // 非人像图片
      ];
      
      print('开始人像检测...');
      final results = await _service.detectPortraitsOnly(imageFiles);
      
      print('📸 人像检测结果:');
      for (int i = 0; i < results.length; i++) {
        final result = results[i];
        print('\n图片${i + 1}:');
        print('  - 检测结果: ${result.hasPortrait ? "✅ 检测到人像" : "❌ 未检测到人像"}');
        print('  - 置信度: ${(result.confidence * 100).toStringAsFixed(1)}%');
        print('  - 适合婚纱照: ${result.isSuitableForWedding ? "✅ 是" : "❌ 否"}');
        print('  - 消息: ${result.message}');
        
        if (result.issues.isNotEmpty) {
          print('  - 问题: ${result.issues.join(", ")}');
        }
        
        if (result.recommendations.isNotEmpty) {
          print('  - 建议: ${result.recommendations.join(", ")}');
        }
      }
    } catch (e) {
      print('❌ 人像检测异常: $e');
    }
  }

  /// 示例5: 检查服务状态和配置
  Future<void> checkServiceStatusExample() async {
    try {
      await SecureConfig.initialize();
      
      print('🔧 检查服务状态...');
      final status = await _service.getServiceStatus();
      
      print('服务状态: ${status['status']}');
      print('配置有效: ${status['configValid']}');
      print('API密钥有效: ${status['apiKeyValid']}');
      print('脱敏API密钥: ${status['maskedApiKey']}');
      print('最大图片数量: ${status['maxImageCount']}');
      print('最大图片大小: ${status['maxImageSize']}');
      print('支持的格式: ${status['supportedFormats']}');
      print('可用风格: ${status['availableStyles']}');
      print('可用场景: ${status['availableScenes']}');
      
    } catch (e) {
      print('❌ 检查状态异常: $e');
    }
  }

  /// 示例6: 配置API密钥
  Future<void> configureApiKeyExample() async {
    try {
      print('🔑 配置API密钥示例...');
      
      // 方法1: 通过代码设置（仅用于测试）
      SecureConfig.setApiKey('your_actual_api_key_here');
      
      // 检查配置
      final validation = SecureConfig.validateConfig();
      print('配置验证结果: $validation');
      
      // 清除密钥（安全考虑）
      SecureConfig.clearApiKey();
      
      print('注意: 生产环境中应该通过环境变量或配置文件设置API密钥');
      
    } catch (e) {
      print('❌ 配置API密钥异常: $e');
    }
  }

  /// 运行所有示例
  Future<void> runAllExamples() async {
    print('🚀 开始运行婚纱照生成示例...\n');
    
    // 检查服务状态
    await checkServiceStatusExample();
    print('\n' + '='*50 + '\n');
    
    // 配置示例
    await configureApiKeyExample();
    print('\n' + '='*50 + '\n');
    
    // 人像检测示例
    await portraitDetectionOnlyExample();
    print('\n' + '='*50 + '\n');
    
    // 注意：以下示例需要真实的图片文件和有效的API密钥
    print('注意: 以下示例需要真实的图片文件路径和有效的API密钥');
    print('请在实际使用时取消注释并提供正确的文件路径');
    
    // await quickGenerateExample();
    // print('\n' + '='*50 + '\n');
    
    // await premiumGenerateExample();
    // print('\n' + '='*50 + '\n');
    
    // await completeWorkflowExample();
    
    print('\n✅ 示例演示完成！');
  }
}

/// Flutter Widget示例
class WeddingPhotoExampleWidget extends StatefulWidget {
  const WeddingPhotoExampleWidget({Key? key}) : super(key: key);

  @override
  State<WeddingPhotoExampleWidget> createState() => _WeddingPhotoExampleWidgetState();
}

class _WeddingPhotoExampleWidgetState extends State<WeddingPhotoExampleWidget> {
  final WeddingPhotoExample _example = WeddingPhotoExample();
  final WeddingPhotoGenerationService _service = WeddingPhotoGenerationService();
  
  bool _isLoading = false;
  String _result = '';
  Map<String, dynamic>? _serviceStatus;

  @override
  void initState() {
    super.initState();
    _checkServiceStatus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('婚纱照生成服务'),
        backgroundColor: Colors.pink[100],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 服务状态卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '服务状态',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    if (_serviceStatus != null) ...[
                      _buildStatusRow('状态', _serviceStatus!['status']),
                      _buildStatusRow('配置有效', _serviceStatus!['configValid']),
                      _buildStatusRow('API密钥', _serviceStatus!['maskedApiKey']),
                    ] else
                      const Text('检查中...'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // 操作按钮
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _checkServiceStatus,
                  icon: const Icon(Icons.refresh),
                  label: const Text('检查状态'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _runPortraitDetection,
                  icon: const Icon(Icons.face),
                  label: const Text('人像检测'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _runQuickGenerate,
                  icon: const Icon(Icons.photo_camera),
                  label: const Text('快速生成'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _runAllExamples,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('运行示例'),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 结果显示
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            '执行结果',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const Spacer(),
                          if (_isLoading)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _result.isEmpty ? '点击按钮开始测试...' : _result,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, dynamic value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text('$label: '),
          Text(
            value.toString(),
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: _getStatusColor(value),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(dynamic value) {
    if (value == 'ready' || value == true) return Colors.green;
    if (value == 'error' || value == false) return Colors.red;
    return Colors.grey;
  }

  Future<void> _checkServiceStatus() async {
    setState(() {
      _isLoading = true;
      _result = '检查服务状态中...';
    });

    try {
      await SecureConfig.initialize();
      final status = await _service.getServiceStatus();
      
      setState(() {
        _serviceStatus = status;
        _result = '服务状态检查完成:\n${_formatJson(status)}';
      });
    } catch (e) {
      setState(() {
        _result = '检查服务状态失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _runPortraitDetection() async {
    setState(() {
      _isLoading = true;
      _result = '运行人像检测示例...';
    });

    try {
      await _example.portraitDetectionOnlyExample();
      setState(() {
        _result = '人像检测示例完成，请查看控制台输出';
      });
    } catch (e) {
      setState(() {
        _result = '人像检测示例失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _runQuickGenerate() async {
    setState(() {
      _isLoading = true;
      _result = '运行快速生成示例...';
    });

    try {
      await _example.quickGenerateExample();
      setState(() {
        _result = '快速生成示例完成，请查看控制台输出';
      });
    } catch (e) {
      setState(() {
        _result = '快速生成示例失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _runAllExamples() async {
    setState(() {
      _isLoading = true;
      _result = '运行所有示例...';
    });

    try {
      await _example.runAllExamples();
      setState(() {
        _result = '所有示例运行完成，请查看控制台输出';
      });
    } catch (e) {
      setState(() {
        _result = '运行示例失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _formatJson(Map<String, dynamic> json) {
    final buffer = StringBuffer();
    json.forEach((key, value) {
      buffer.writeln('$key: $value');
    });
    return buffer.toString();
  }
}
