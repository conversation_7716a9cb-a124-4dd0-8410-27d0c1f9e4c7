import 'package:dartz/dartz.dart';

import '../../../../core/error/network_exceptions.dart';
// import '../../../../core/services/firebase_auth_service.dart';  // 暂时注释
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_local_datasource.dart';

/// 认证仓库实现 - 暂时注释Firebase相关功能
class AuthRepositoryImpl implements AuthRepository {
  // final FirebaseAuthService _firebaseAuthService;  // 暂时注释
  final AuthLocalDataSource _localDataSource;

  AuthRepositoryImpl(/* this._firebaseAuthService, */ this._localDataSource);
  
  @override
  Future<Either<NetworkExceptions, User>> login(String email, String password) async {
    // 暂时返回模拟用户
    await Future.delayed(const Duration(seconds: 1));

    final mockUser = User(
      id: 'mock_user_id',
      email: email,
      name: '模拟用户',
    );

    // 保存用户信息到本地
    await _localDataSource.saveUser(mockUser);
    return Right(mockUser);

    // TODO: 重新启用Firebase认证
    /*
    final result = await _firebaseAuthService.signInWithEmailAndPassword(
      email: email,
      password: password,
    );

    return result.fold(
      (failure) => Left(failure),
      (user) async {
        // 保存用户信息到本地
        await _localDataSource.saveUserData(user);
        return Right(user);
      },
    );
    */
  }

  @override
  Future<Either<NetworkExceptions, User>> register(String email, String password, String? name) async {
    final result = await _firebaseAuthService.registerWithEmailAndPassword(
      email: email,
      password: password,
      name: name,
    );

    return result.fold(
      (failure) => Left(failure),
      (user) async {
        // 保存用户信息到本地
        await _localDataSource.saveUserData(user);
        return Right(user);
      },
    );
  }

  @override
  Future<Either<NetworkExceptions, User>> signInWithGoogle() async {
    final result = await _firebaseAuthService.signInWithGoogle();

    return result.fold(
      (failure) => Left(failure),
      (user) async {
        // 保存用户信息到本地
        await _localDataSource.saveUserData(user);
        return Right(user);
      },
    );
  }

  @override
  Future<Either<NetworkExceptions, User>> signInWithApple() async {
    final result = await _firebaseAuthService.signInWithApple();

    return result.fold(
      (failure) => Left(failure),
      (user) async {
        // 保存用户信息到本地
        await _localDataSource.saveUserData(user);
        return Right(user);
      },
    );
  }
  
  @override
  Future<Either<NetworkExceptions, void>> logout() async {
    final result = await _firebaseAuthService.signOut();

    return result.fold(
      (failure) => Left(failure),
      (_) async {
        // 清除本地数据
        await _localDataSource.clearAuthData();
        return const Right(null);
      },
    );
  }

  @override
  Future<User?> getCurrentUser() async {
    // 首先检查Firebase当前用户
    final firebaseUser = _firebaseAuthService.currentUser;
    if (firebaseUser != null) {
      return firebaseUser;
    }

    // 如果Firebase没有用户，检查本地存储
    try {
      return await _localDataSource.getUserData();
    } catch (e) {
      return null;
    }
  }

  @override
  Stream<User?> get userChanges => _firebaseAuthService.userChanges;

  @override
  Future<bool> isLoggedIn() async {
    return _firebaseAuthService.currentUser != null;
  }

  @override
  Future<Either<NetworkExceptions, void>> sendPasswordResetEmail(String email) async {
    return await _firebaseAuthService.sendPasswordResetEmail(email: email);
  }

  @override
  Future<Either<NetworkExceptions, void>> sendEmailVerification() async {
    return await _firebaseAuthService.sendEmailVerification();
  }

  @override
  Future<Either<NetworkExceptions, void>> deleteAccount() async {
    final result = await _firebaseAuthService.deleteAccount();

    return result.fold(
      (failure) => Left(failure),
      (_) async {
        // 清除本地数据
        await _localDataSource.clearAuthData();
        return const Right(null);
      },
    );
  }
}
