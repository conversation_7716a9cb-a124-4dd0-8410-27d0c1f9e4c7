import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/services/permission_service.dart';
import '../../../../core/theme/wedding_colors.dart';
import '../../../../core/utils/log_service.dart';
import '../../../../shared/widgets/permission_dialog.dart';

/// 权限管理页面
@RoutePage()
class PermissionsScreen extends ConsumerStatefulWidget {
  const PermissionsScreen({super.key});

  @override
  ConsumerState<PermissionsScreen> createState() => _PermissionsScreenState();
}

class _PermissionsScreenState extends ConsumerState<PermissionsScreen> {
  final LogService _logger = LogService();
  final PermissionService _permissionService = PermissionService();
  
  Map<PermissionType, PermissionResult> _permissionStatuses = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPermissionStatuses();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          '权限管理',
          style: TextStyle(
            fontFamily: 'Playfair Display',
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: WeddingColors.darkGray,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: WeddingColors.primaryPink,
            ),
            onPressed: _refreshPermissions,
            tooltip: '刷新权限状态',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(),
                  SizedBox(height: 24.h),
                  _buildPermissionsList(),
                  SizedBox(height: 24.h),
                  _buildQuickActions(),
                ],
              ),
            ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: WeddingColors.weddingGradient,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: WeddingColors.shadowMedium,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.security,
                color: WeddingColors.pureWhite,
                size: 24.sp,
              ),
              SizedBox(width: 12.w),
              Text(
                '权限状态',
                style: TextStyle(
                  fontFamily: 'Playfair Display',
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w600,
                  color: WeddingColors.pureWhite,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            '管理应用权限，确保功能正常使用。点击权限项可以重新请求或查看详情。',
            style: TextStyle(
              fontFamily: 'SF Pro Display',
              fontSize: 14.sp,
              color: WeddingColors.pureWhite.withOpacity(0.9),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '应用权限',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        _buildPermissionItem(
          PermissionType.camera,
          '相机权限',
          '用于拍摄照片生成AI婚纱照',
        ),
        SizedBox(height: 12.h),
        _buildPermissionItem(
          PermissionType.photos,
          '相册权限',
          '用于选择相册中的照片',
        ),
        SizedBox(height: 12.h),
        _buildPermissionItem(
          PermissionType.notification,
          '通知权限',
          '用于发送生成完成通知',
        ),
      ],
    );
  }

  Widget _buildPermissionItem(
    PermissionType type,
    String title,
    String description,
  ) {
    final status = _permissionStatuses[type] ?? PermissionResult.denied;
    
    return PermissionStatusIndicator(
      type: type,
      status: status,
      onTap: () => _handlePermissionTap(type),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '快捷操作',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                '请求所有权限',
                Icons.check_circle_outline,
                Colors.green,
                _requestAllPermissions,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildActionButton(
                '打开系统设置',
                Icons.settings,
                Colors.blue,
                _openSystemSettings,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 18.sp),
      label: Text(text),
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withOpacity(0.1),
        foregroundColor: color,
        elevation: 0,
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
          side: BorderSide(color: color.withOpacity(0.3)),
        ),
      ),
    );
  }

  /// 加载权限状态
  Future<void> _loadPermissionStatuses() async {
    setState(() => _isLoading = true);
    
    try {
      _logger.d('加载权限状态');
      
      final types = [
        PermissionType.camera,
        PermissionType.photos,
        PermissionType.notification,
      ];
      
      for (final type in types) {
        final status = await _permissionService.checkPermission(type);
        _permissionStatuses[type] = status;
      }
      
      _logger.i('权限状态加载完成: $_permissionStatuses');
    } catch (e) {
      _logger.e('加载权限状态失败', e);
      _showErrorSnackBar('加载权限状态失败');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 刷新权限状态
  Future<void> _refreshPermissions() async {
    _logger.d('刷新权限状态');
    await _loadPermissionStatuses();
    _showSuccessSnackBar('权限状态已刷新');
  }

  /// 处理权限项点击
  Future<void> _handlePermissionTap(PermissionType type) async {
    final currentStatus = _permissionStatuses[type];

    if (currentStatus == PermissionResult.granted ||
        currentStatus == PermissionResult.limited) {
      _showInfoDialog(type, '权限已授权', '该权限已经授权，功能可以正常使用。');
      return;
    }
    
    if (currentStatus == PermissionResult.permanentlyDenied) {
      final permissionName = _getPermissionName(type);
      final result = await PermissionDialog.showPermanentlyDeniedDialog(
        context,
        type,
        permissionName,
      );
      
      if (result == true) {
        await _permissionService.openSettings();
      }
      return;
    }
    
    // 请求权限
    await _requestSinglePermission(type);
  }

  /// 请求单个权限
  Future<void> _requestSinglePermission(PermissionType type) async {
    try {
      _logger.d('请求单个权限: ${type.name}');
      
      final result = await _permissionService.requestPermission(type);
      setState(() {
        _permissionStatuses[type] = result;
      });
      
      final permissionName = _getPermissionName(type);
      if (result == PermissionResult.granted ||
          result == PermissionResult.limited) {
        _showSuccessSnackBar('${permissionName}权限已授权');
      } else {
        _showErrorSnackBar('${permissionName}权限被拒绝');
      }
    } catch (e) {
      _logger.e('请求权限失败: ${type.name}', e);
      _showErrorSnackBar('请求权限失败');
    }
  }

  /// 请求所有权限
  Future<void> _requestAllPermissions() async {
    try {
      _logger.d('请求所有权限');
      
      final types = [
        PermissionType.camera,
        PermissionType.photos,
        PermissionType.notification,
      ];
      
      final results = await _permissionService.requestMultiplePermissions(types);
      
      setState(() {
        _permissionStatuses.addAll(results);
      });
      
      final grantedCount = results.values
          .where((status) =>
              status == PermissionResult.granted ||
              status == PermissionResult.limited)
          .length;
      
      _showSuccessSnackBar('已授权 $grantedCount/${types.length} 个权限');
    } catch (e) {
      _logger.e('批量请求权限失败', e);
      _showErrorSnackBar('批量请求权限失败');
    }
  }

  /// 打开系统设置
  Future<void> _openSystemSettings() async {
    try {
      _logger.d('打开系统设置');
      final success = await _permissionService.openSettings();
      if (!success) {
        _showErrorSnackBar('无法打开系统设置');
      }
    } catch (e) {
      _logger.e('打开系统设置失败', e);
      _showErrorSnackBar('打开系统设置失败');
    }
  }

  /// 显示信息对话框
  void _showInfoDialog(PermissionType type, String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 获取权限名称
  String _getPermissionName(PermissionType type) {
    switch (type) {
      case PermissionType.camera:
        return '相机';
      case PermissionType.photos:
      case PermissionType.storage:
        return '相册';
      case PermissionType.notification:
        return '通知';
      case PermissionType.microphone:
        return '麦克风';
      case PermissionType.location:
        return '位置';
    }
  }

  /// 显示成功提示
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
