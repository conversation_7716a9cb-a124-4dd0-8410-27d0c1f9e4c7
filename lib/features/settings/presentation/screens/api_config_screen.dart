import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/services/environment_service.dart';
import '../../../../core/config/app_config.dart';
import '../../../../core/theme/wedding_colors.dart';
import '../../../../core/theme/design_system/wedding_buttons.dart';

/// API配置界面（开发和测试用）
class ApiConfigScreen extends StatefulWidget {
  const ApiConfigScreen({super.key});

  @override
  State<ApiConfigScreen> createState() => _ApiConfigScreenState();
}

class _ApiConfigScreenState extends State<ApiConfigScreen> {
  final _formKey = GlobalKey<FormState>();
  final _apiKeyController = TextEditingController();
  bool _isObscured = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCurrentConfig();
  }

  @override
  void dispose() {
    _apiKeyController.dispose();
    super.dispose();
  }

  /// 加载当前配置
  void _loadCurrentConfig() {
    if (ApiConfig.hasApiKey) {
      // 只显示API密钥的前几位和后几位，中间用*代替
      final apiKey = ApiConfig.apiKey;
      if (apiKey.length > 10) {
        _apiKeyController.text = '${apiKey.substring(0, 4)}${'*' * (apiKey.length - 8)}${apiKey.substring(apiKey.length - 4)}';
      } else {
        _apiKeyController.text = '*' * apiKey.length;
      }
    }
  }

  /// 保存API配置
  Future<void> _saveConfig() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final apiKey = _apiKeyController.text.trim();
      
      // 验证API密钥格式
      if (!EnvironmentService.validateApiKey(apiKey)) {
        throw Exception('API密钥格式无效');
      }

      // 设置API密钥
      EnvironmentService.setApiKey(apiKey);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                const Text('API配置保存成功'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );

        // 返回上一页
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('保存失败: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 测试API连接
  Future<void> _testConnection() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final apiKey = _apiKeyController.text.trim();
      
      // 临时设置API密钥进行测试
      final originalKey = ApiConfig.hasApiKey ? ApiConfig.apiKey : null;
      EnvironmentService.setApiKey(apiKey);

      // 这里可以添加实际的API测试调用
      // 暂时只做格式验证
      await Future.delayed(const Duration(seconds: 1));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.wifi_protected_setup, color: Colors.white),
                const SizedBox(width: 8),
                const Text('API连接测试成功'),
              ],
            ),
            backgroundColor: Colors.blue,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }

      // 恢复原始密钥
      if (originalKey != null) {
        EnvironmentService.setApiKey(originalKey);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('连接测试失败: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final configStatus = EnvironmentService.getConfigStatus();

    return Scaffold(
      backgroundColor: WeddingColors.background,
      appBar: AppBar(
        title: const Text('API配置'),
        backgroundColor: WeddingColors.background,
        elevation: 0,
        foregroundColor: WeddingColors.textPrimary,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(24.w),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 配置状态卡片
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '当前配置状态',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: WeddingColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 12.h),
                    _buildStatusItem('API密钥', configStatus['hasApiKey'] ? '已配置' : '未配置', 
                        configStatus['hasApiKey']),
                    _buildStatusItem('密钥有效性', configStatus['apiKeyValid'] ? '有效' : '无效', 
                        configStatus['apiKeyValid']),
                    _buildStatusItem('API地址', configStatus['baseUrl'], true),
                    _buildStatusItem('环境', configStatus['environment'], true),
                  ],
                ),
              ),

              SizedBox(height: 24.h),

              // API密钥输入
              Text(
                'API密钥配置',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: WeddingColors.textPrimary,
                ),
              ),
              SizedBox(height: 12.h),

              TextFormField(
                controller: _apiKeyController,
                obscureText: _isObscured,
                decoration: InputDecoration(
                  labelText: 'API密钥',
                  hintText: '请输入您的API密钥',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  suffixIcon: IconButton(
                    icon: Icon(_isObscured ? Icons.visibility : Icons.visibility_off),
                    onPressed: () {
                      setState(() {
                        _isObscured = !_isObscured;
                      });
                    },
                  ),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入API密钥';
                  }
                  if (!EnvironmentService.validateApiKey(value.trim())) {
                    return 'API密钥格式无效';
                  }
                  return null;
                },
              ),

              SizedBox(height: 24.h),

              // 操作按钮
              Row(
                children: [
                  Expanded(
                    child: WeddingButtons.secondary(
                      text: '测试连接',
                      onPressed: _isLoading ? () {} : _testConnection,
                      isLoading: _isLoading,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: WeddingButtons.primary(
                      text: '保存配置',
                      onPressed: _isLoading ? () {} : _saveConfig,
                      isLoading: _isLoading,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 24.h),

              // 说明文本
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '配置说明',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[700],
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      '• API密钥用于访问AI图片生成服务\n'
                      '• 请确保密钥有效且有足够的配额\n'
                      '• 密钥将安全存储在本地\n'
                      '• 生产环境建议通过环境变量配置',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.blue[600],
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusItem(String label, String value, bool isValid) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              color: WeddingColors.textSecondary,
            ),
          ),
          Row(
            children: [
              Icon(
                isValid ? Icons.check_circle : Icons.error,
                size: 16.sp,
                color: isValid ? Colors.green : Colors.red,
              ),
              SizedBox(width: 4.w),
              Text(
                value,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: isValid ? Colors.green : Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
