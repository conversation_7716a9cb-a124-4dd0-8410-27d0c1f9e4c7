import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../core/theme/app_theme.dart';
import '../../../../../core/theme/wedding_colors.dart';
import '../../../../../core/providers/app_providers.dart';
import '../../../../../core/router/app_router.dart';
import '../../../auth/presentation/providers/auth_provider.dart';

@RoutePage()
class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appState = ref.watch(appProvider);
    // final authState = ref.watch(authProvider);  // 暂时注释
    final isDarkMode = appState.themeMode == ThemeMode.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          '设置',
          style: TextStyle(
            fontFamily: 'Playfair Display',
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: WeddingColors.darkGray,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: ListView(
        children: [
          ListTile(
            leading: Icon(
              Icons.brightness_6,
              color: WeddingColors.primaryPink,
            ),
            title: Text(
              '深色模式',
              style: TextStyle(
                fontFamily: 'SF Pro Display',
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: WeddingColors.darkGray,
              ),
            ),
            trailing: Switch(
              value: isDarkMode,
              activeColor: WeddingColors.primaryPink,
              onChanged: (value) {
                final newMode = value ? ThemeMode.dark : ThemeMode.light;
                ref.read(appProvider.notifier).setThemeMode(newMode);
              },
            ),
          ),
          const Divider(),
          ListTile(
            leading: Icon(
              Icons.language,
              color: WeddingColors.primaryPink,
            ),
            title: Text(
              '语言',
              style: TextStyle(
                fontFamily: 'SF Pro Display',
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: WeddingColors.darkGray,
              ),
            ),
            subtitle: Text(
              '简体中文',
              style: TextStyle(
                fontFamily: 'SF Pro Display',
                fontSize: 14.sp,
                color: WeddingColors.mediumGray,
              ),
            ),
            onTap: () {
              // 切换语言
            },
          ),
          const Divider(),
          ListTile(
            leading: Icon(
              Icons.notifications,
              color: WeddingColors.primaryPink,
            ),
            title: Text(
              '通知',
              style: TextStyle(
                fontFamily: 'SF Pro Display',
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: WeddingColors.darkGray,
              ),
            ),
            onTap: () {
              // 打开通知设置
            },
          ),
          const Divider(),
          ListTile(
            leading: Icon(
              Icons.security,
              color: WeddingColors.primaryPink,
            ),
            title: Text(
              '权限管理',
              style: TextStyle(
                fontFamily: 'SF Pro Display',
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: WeddingColors.darkGray,
              ),
            ),
            subtitle: Text(
              '管理应用权限设置',
              style: TextStyle(
                fontFamily: 'SF Pro Display',
                fontSize: 14.sp,
                color: WeddingColors.mediumGray,
              ),
            ),
            trailing: Icon(
              Icons.arrow_forward_ios,
              size: 16.sp,
              color: WeddingColors.mediumGray,
            ),
            onTap: () {
              context.router.push(const PermissionsRoute());
            },
          ),
          const Divider(),
          ListTile(
            leading: Icon(
              Icons.privacy_tip,
              color: WeddingColors.primaryPink,
            ),
            title: Text(
              '隐私与安全',
              style: TextStyle(
                fontFamily: 'SF Pro Display',
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: WeddingColors.darkGray,
              ),
            ),
            onTap: () {
              // 打开隐私设置
            },
          ),
          const Divider(),
          ListTile(
            leading: Icon(
              Icons.info_outline,
              color: WeddingColors.primaryPink,
            ),
            title: Text(
              '关于',
              style: TextStyle(
                fontFamily: 'SF Pro Display',
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: WeddingColors.darkGray,
              ),
            ),
            onTap: () {
              // 显示关于信息
              showAboutDialog(
                context: context,
                applicationName: 'Hera',
                applicationVersion: '1.0.0',
                applicationLegalese: '© 2024 Hera AI婚纱照生成',
                applicationIcon: Icon(
                  Icons.favorite,
                  size: 50.sp,
                  color: WeddingColors.primaryPink,
                ),
              );
            },
          ),
          const Divider(),
          // 登录相关功能暂时注释
          // if (authState.isAuthenticated) ...[
          //   const Divider(),
          //   ListTile(
          //     leading: const Icon(Icons.logout, color: Colors.red),
          //     title: const Text('退出登录', style: TextStyle(color: Colors.red)),
          //     onTap: () {
          //       // 退出登录确认
          //       showDialog(
          //         context: context,
          //         builder: (context) => AlertDialog(
          //           title: const Text('确认退出登录'),
          //           content: const Text('您确定要退出登录吗？'),
          //           actions: [
          //             TextButton(
          //               onPressed: () => Navigator.of(context).pop(),
          //               child: const Text('取消'),
          //             ),
          //             TextButton(
          //               onPressed: () async {
          //                 Navigator.of(context).pop();
          //                 // 执行退出登录逻辑
          //                 await ref.read(authProvider.notifier).logout();
          //                 // 导航到登录页面
          //                 if (context.mounted) {
          //                   context.router.replace(const AuthRoute());
          //                 }
          //               },
          //               child: const Text('确定'),
          //             ),
          //           ],
          //         ),
          //       );
          //     },
          //   ),
          // ],
        ],
      ),
    );
  }
}