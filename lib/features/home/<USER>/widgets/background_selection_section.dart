import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/wedding_colors.dart';

/// Background selection section widget matching the prototype design
class BackgroundSelectionSection extends StatefulWidget {
  final String? selectedBackground;
  final Function(String) onBackgroundSelected;

  const BackgroundSelectionSection({
    super.key,
    this.selectedBackground,
    required this.onBackgroundSelected,
  });

  @override
  State<BackgroundSelectionSection> createState() => _BackgroundSelectionSectionState();
}

class _BackgroundSelectionSectionState extends State<BackgroundSelectionSection> {
  bool _showPremiumBackgrounds = false;

  // Basic backgrounds (free)
  final List<BackgroundOption> _basicBackgrounds = [
    BackgroundOption(
      id: 'beach',
      name: '海滩',
      imageUrl: 'https://via.placeholder.com/200x120/87ceeb/ffffff?text=海滩',
      isPremium: false,
    ),
    BackgroundOption(
      id: 'garden',
      name: '花园',
      imageUrl: 'https://via.placeholder.com/200x120/90ee90/ffffff?text=花园',
      isPremium: false,
    ),
    BackgroundOption(
      id: 'church',
      name: '教堂',
      imageUrl: 'https://via.placeholder.com/200x120/dda0dd/ffffff?text=教堂',
      isPremium: false,
    ),
    BackgroundOption(
      id: 'mountain',
      name: '山景',
      imageUrl: 'https://via.placeholder.com/200x120/8fbc8f/ffffff?text=山景',
      isPremium: false,
    ),
  ];

  // Premium backgrounds
  final List<BackgroundOption> _premiumBackgrounds = [
    BackgroundOption(
      id: 'castle',
      name: '古堡',
      imageUrl: 'https://via.placeholder.com/200x120/d4af37/ffffff?text=城堡',
      isPremium: true,
    ),
    BackgroundOption(
      id: 'lavender',
      name: '薰衣草田',
      imageUrl: 'https://via.placeholder.com/200x120/9370db/ffffff?text=薰衣草',
      isPremium: true,
    ),
    BackgroundOption(
      id: 'sakura',
      name: '樱花林',
      imageUrl: 'https://via.placeholder.com/200x120/ffb6c1/ffffff?text=樱花',
      isPremium: true,
    ),
    BackgroundOption(
      id: 'palace',
      name: '皇宫',
      imageUrl: 'https://via.placeholder.com/200x120/ffd700/ffffff?text=宫殿',
      isPremium: true,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '选择背景场景',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: WeddingColors.darkGray,
              ),
            ),
            GestureDetector(
              onTap: () => setState(() => _showPremiumBackgrounds = !_showPremiumBackgrounds),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: WeddingColors.lightGray,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _showPremiumBackgrounds ? Icons.visibility_off : Icons.visibility,
                      size: 16.sp,
                      color: WeddingColors.primaryPink,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      _showPremiumBackgrounds ? '收起' : '查看更多',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: WeddingColors.primaryPink,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        
        SizedBox(height: 16.h),
        
        // Basic backgrounds grid
        _buildBackgroundGrid(_basicBackgrounds),
        
        // Premium backgrounds grid (if shown)
        if (_showPremiumBackgrounds) ...[
          SizedBox(height: 16.h),
          _buildBackgroundGrid(_premiumBackgrounds),
        ],
      ],
    );
  }

  Widget _buildBackgroundGrid(List<BackgroundOption> backgrounds) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.h,
        childAspectRatio: 1.6,
      ),
      itemCount: backgrounds.length,
      itemBuilder: (context, index) => _buildBackgroundCard(backgrounds[index]),
    );
  }

  Widget _buildBackgroundCard(BackgroundOption background) {
    final isSelected = widget.selectedBackground == background.id;
    
    return GestureDetector(
      onTap: () {
        if (background.isPremium) {
          // Show premium upgrade dialog
          _showPremiumDialog();
        } else {
          widget.onBackgroundSelected(background.id);
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isSelected ? WeddingColors.primaryPink : WeddingColors.lightGray,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: WeddingColors.primaryPink.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ] : null,
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Background image
                Expanded(
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: WeddingColors.lightGray,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12.r),
                        topRight: Radius.circular(12.r),
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12.r),
                        topRight: Radius.circular(12.r),
                      ),
                      child: Image.network(
                        background.imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                          color: WeddingColors.lightGray,
                          child: Icon(
                            Icons.landscape,
                            size: 40.sp,
                            color: WeddingColors.mediumGray,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                
                // Background name
                Padding(
                  padding: EdgeInsets.all(12.w),
                  child: Text(
                    background.name,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: WeddingColors.darkGray,
                    ),
                  ),
                ),
              ],
            ),
            
            // Premium overlay
            if (background.isPremium)
              Positioned(
                top: 8.w,
                right: 8.w,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: WeddingColors.accentGold,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.workspace_premium,
                        size: 12.sp,
                        color: WeddingColors.pureWhite,
                      ),
                      SizedBox(width: 2.w),
                      Text(
                        '会员专享',
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: WeddingColors.pureWhite,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showPremiumDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('需要升级会员'),
        content: const Text('此背景需要会员权限，升级会员即可解锁所有高级功能。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('稍后'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to subscription
            },
            child: const Text('立即升级'),
          ),
        ],
      ),
    );
  }
}

/// Background option data class
class BackgroundOption {
  final String id;
  final String name;
  final String imageUrl;
  final bool isPremium;

  const BackgroundOption({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.isPremium,
  });
}
