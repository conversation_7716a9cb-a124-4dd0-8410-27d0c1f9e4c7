import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/wedding_colors.dart';

/// User menu dropdown widget matching the prototype design
class UserMenu extends StatelessWidget {
  final VoidCallback onClose;
  final VoidCallback onLoginTap;
  final VoidCallback onSubscriptionTap;
  final VoidCallback onSettingsTap;

  const UserMenu({
    super.key,
    required this.onClose,
    required this.onLoginTap,
    required this.onSubscriptionTap,
    required this.onSettingsTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onClose,
      child: Container(
        color: Colors.black.withOpacity(0.3),
        child: Stack(
          children: [
            // Positioned menu
            Positioned(
              top: 100.h, // Below header
              right: 16.w,
              child: Material(
                elevation: 8,
                borderRadius: BorderRadius.circular(12.r),
                child: Container(
                  width: 280.w,
                  decoration: BoxDecoration(
                    color: WeddingColors.pureWhite,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // User status section
                      Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          color: WeddingColors.lightGray,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(12.r),
                            topRight: Radius.circular(12.r),
                          ),
                        ),
                        child: Row(
                          children: [
                            // User avatar
                            Container(
                              width: 48.w,
                              height: 48.w,
                              decoration: BoxDecoration(
                                color: WeddingColors.primaryPinkLight,
                                borderRadius: BorderRadius.circular(24.r),
                              ),
                              child: Icon(
                                Icons.person,
                                size: 24.sp,
                                color: WeddingColors.primaryPink,
                              ),
                            ),
                            SizedBox(width: 12.w),
                            
                            // User info
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '游客用户',
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w600,
                                      color: WeddingColors.darkGray,
                                    ),
                                  ),
                                  SizedBox(height: 2.h),
                                  Text(
                                    '本地使用模式',
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: WeddingColors.mediumGray,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Divider
                      Container(
                        height: 1.h,
                        color: WeddingColors.lightGray,
                      ),
                      
                      // Menu items
                      Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.h),
                        child: Column(
                          children: [
                            _buildMenuItem(
                              icon: Icons.login,
                              title: '登录账户',
                              subtitle: '同步数据',
                              onTap: () {
                                onClose();
                                onLoginTap();
                              },
                            ),
                            _buildMenuItem(
                              icon: Icons.workspace_premium,
                              title: '会员订阅',
                              onTap: () {
                                onClose();
                                onSubscriptionTap();
                              },
                            ),
                            _buildMenuItem(
                              icon: Icons.settings,
                              title: '设置',
                              onTap: () {
                                onClose();
                                onSettingsTap();
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20.sp,
              color: WeddingColors.primaryPink,
            ),
            SizedBox(width: 12.w),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: WeddingColors.darkGray,
                    ),
                  ),
                  if (subtitle != null) ...[
                    SizedBox(height: 2.h),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                      decoration: BoxDecoration(
                        color: WeddingColors.accentGoldLight,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: WeddingColors.accentGoldDark,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            
            Icon(
              Icons.chevron_right,
              size: 16.sp,
              color: WeddingColors.mediumGray,
            ),
          ],
        ),
      ),
    );
  }
}
