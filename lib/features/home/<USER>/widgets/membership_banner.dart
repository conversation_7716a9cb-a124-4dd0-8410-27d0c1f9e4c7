import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/wedding_colors.dart';

/// Membership banner widget matching the prototype design
class MembershipBanner extends StatelessWidget {
  final bool isPremiumUser;
  final int remainingGenerations;
  final VoidCallback? onUpgradeTap;

  const MembershipBanner({
    super.key,
    this.isPremiumUser = false,
    this.remainingGenerations = 3,
    this.onUpgradeTap,
  });

  @override
  Widget build(BuildContext context) {
    if (isPremiumUser) {
      return const SizedBox.shrink(); // Hide banner for premium users
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            WeddingColors.accentGoldLight.withOpacity(0.1),
            WeddingColors.accentGold.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: WeddingColors.accentGold.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Info section
          Expanded(
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 20.sp,
                  color: WeddingColors.accentGold,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    '今日剩余生成次数：$remainingGenerations次',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: WeddingColors.darkGray,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Upgrade button
          GestureDetector(
            onTap: onUpgradeTap,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                gradient: WeddingColors.goldGradient,
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: WeddingColors.accentGold.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.workspace_premium,
                    size: 16.sp,
                    color: WeddingColors.pureWhite,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    '升级会员',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: WeddingColors.pureWhite,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
