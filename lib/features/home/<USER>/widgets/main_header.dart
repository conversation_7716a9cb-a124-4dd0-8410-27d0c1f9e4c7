import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/wedding_colors.dart';

/// Main header widget matching the prototype design
class MainHeader extends StatelessWidget {
  final VoidCallback onUserMenuTap;
  final VoidCallback onMembershipTap;
  final VoidCallback onGalleryTap;

  const MainHeader({
    super.key,
    required this.onUserMenuTap,
    required this.onMembershipTap,
    required this.onGalleryTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: WeddingColors.pureWhite,
        boxShadow: [
          BoxShadow(
            color: WeddingColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Left side - App title
            Text(
              'AI婚纱照',
              style: TextStyle(
                fontFamily: 'Dancing Script',
                fontSize: 24.sp,
                fontWeight: FontWeight.w600,
                color: WeddingColors.primaryPink,
              ),
            ),
            
            // Right side - Action buttons
            Row(
              children: [
                // Membership button
                _buildIconButton(
                  icon: Icons.workspace_premium,
                  onTap: onMembershipTap,
                  tooltip: '会员订阅',
                ),
                SizedBox(width: 8.w),
                
                // User menu button
                _buildIconButton(
                  icon: Icons.account_circle,
                  onTap: onUserMenuTap,
                  tooltip: '用户菜单',
                ),
                SizedBox(width: 8.w),
                
                // Gallery button
                _buildIconButton(
                  icon: Icons.photo_library,
                  onTap: onGalleryTap,
                  tooltip: '我的作品',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIconButton({
    required IconData icon,
    required VoidCallback onTap,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20.r),
        child: Container(
          width: 40.w,
          height: 40.w,
          decoration: BoxDecoration(
            color: WeddingColors.lightGray,
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Icon(
            icon,
            size: 20.sp,
            color: WeddingColors.primaryPink,
          ),
        ),
      ),
    );
  }
}
