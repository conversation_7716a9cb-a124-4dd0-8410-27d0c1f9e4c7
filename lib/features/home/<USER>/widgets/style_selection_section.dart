import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/wedding_colors.dart';

/// Style selection section widget matching the prototype design
class StyleSelectionSection extends StatefulWidget {
  final String? selectedStyle;
  final Function(String) onStyleSelected;

  const StyleSelectionSection({
    super.key,
    this.selectedStyle,
    required this.onStyleSelected,
  });

  @override
  State<StyleSelectionSection> createState() => _StyleSelectionSectionState();
}

class _StyleSelectionSectionState extends State<StyleSelectionSection> {
  bool _showPremiumStyles = false;

  // Basic styles (free)
  final List<StyleOption> _basicStyles = [
    StyleOption(
      id: 'classic',
      name: '经典款',
      description: '优雅传统',
      imageUrl: 'https://via.placeholder.com/150x200/f8f9fa/6c757d?text=经典款',
      isPremium: false,
    ),
    StyleOption(
      id: 'modern',
      name: '现代款',
      description: '简约时尚',
      imageUrl: 'https://via.placeholder.com/150x200/f8f9fa/6c757d?text=现代款',
      isPremium: false,
    ),
    StyleOption(
      id: 'princess',
      name: '公主款',
      description: '梦幻浪漫',
      imageUrl: 'https://via.placeholder.com/150x200/f8f9fa/6c757d?text=公主款',
      isPremium: false,
    ),
    StyleOption(
      id: 'vintage',
      name: '复古款',
      description: '怀旧典雅',
      imageUrl: 'https://via.placeholder.com/150x200/f8f9fa/6c757d?text=复古款',
      isPremium: false,
    ),
  ];

  // Premium styles
  final List<StyleOption> _premiumStyles = [
    StyleOption(
      id: 'bohemian',
      name: '波西米亚',
      description: '自由浪漫',
      imageUrl: 'https://via.placeholder.com/150x200/f8f9fa/6c757d?text=波西米亚',
      isPremium: true,
    ),
    StyleOption(
      id: 'luxury',
      name: '奢华款',
      description: '华丽典雅',
      imageUrl: 'https://via.placeholder.com/150x200/f8f9fa/6c757d?text=奢华款',
      isPremium: true,
    ),
    StyleOption(
      id: 'minimalist',
      name: '极简款',
      description: '简约纯净',
      imageUrl: 'https://via.placeholder.com/150x200/f8f9fa/6c757d?text=极简款',
      isPremium: true,
    ),
    StyleOption(
      id: 'fairy',
      name: '仙女款',
      description: '飘逸梦幻',
      imageUrl: 'https://via.placeholder.com/150x200/f8f9fa/6c757d?text=仙女款',
      isPremium: true,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '选择婚纱风格',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: WeddingColors.darkGray,
              ),
            ),
            GestureDetector(
              onTap: () => setState(() => _showPremiumStyles = !_showPremiumStyles),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: WeddingColors.lightGray,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _showPremiumStyles ? Icons.visibility_off : Icons.visibility,
                      size: 16.sp,
                      color: WeddingColors.primaryPink,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      _showPremiumStyles ? '收起' : '查看更多',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: WeddingColors.primaryPink,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        
        SizedBox(height: 16.h),
        
        // Basic styles grid
        _buildStyleGrid(_basicStyles),
        
        // Premium styles grid (if shown)
        if (_showPremiumStyles) ...[
          SizedBox(height: 16.h),
          _buildStyleGrid(_premiumStyles),
        ],
      ],
    );
  }

  Widget _buildStyleGrid(List<StyleOption> styles) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.h,
        childAspectRatio: 0.75,
      ),
      itemCount: styles.length,
      itemBuilder: (context, index) => _buildStyleCard(styles[index]),
    );
  }

  Widget _buildStyleCard(StyleOption style) {
    final isSelected = widget.selectedStyle == style.id;
    
    return GestureDetector(
      onTap: () {
        if (style.isPremium) {
          // Show premium upgrade dialog
          _showPremiumDialog();
        } else {
          widget.onStyleSelected(style.id);
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isSelected ? WeddingColors.primaryPink : WeddingColors.lightGray,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: WeddingColors.primaryPink.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ] : null,
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Style image
                Expanded(
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: WeddingColors.lightGray,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12.r),
                        topRight: Radius.circular(12.r),
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12.r),
                        topRight: Radius.circular(12.r),
                      ),
                      child: Image.network(
                        style.imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                          color: WeddingColors.lightGray,
                          child: Icon(
                            Icons.image,
                            size: 40.sp,
                            color: WeddingColors.mediumGray,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                
                // Style info
                Padding(
                  padding: EdgeInsets.all(12.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        style.name,
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: WeddingColors.darkGray,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      Text(
                        style.description,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: WeddingColors.mediumGray,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            // Premium overlay
            if (style.isPremium)
              Positioned(
                top: 8.w,
                right: 8.w,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: WeddingColors.accentGold,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.workspace_premium,
                        size: 12.sp,
                        color: WeddingColors.pureWhite,
                      ),
                      SizedBox(width: 2.w),
                      Text(
                        '会员专享',
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: WeddingColors.pureWhite,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showPremiumDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('需要升级会员'),
        content: const Text('此风格需要会员权限，升级会员即可解锁所有高级功能。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('稍后'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to subscription
            },
            child: const Text('立即升级'),
          ),
        ],
      ),
    );
  }
}

/// Style option data class
class StyleOption {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final bool isPremium;

  const StyleOption({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.isPremium,
  });
}
