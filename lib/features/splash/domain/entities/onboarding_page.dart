import 'package:flutter/material.dart';

/// 引导页面实体
class OnboardingPage {
  final String title;
  final String description;
  final IconData icon;
  final Color? iconColor;
  final String? imagePath;
  final List<String>? features;

  const OnboardingPage({
    required this.title,
    required this.description,
    required this.icon,
    this.iconColor,
    this.imagePath,
    this.features,
  });
}

/// 预定义的引导页面数据
class OnboardingData {
  static const List<OnboardingPage> pages = [
    OnboardingPage(
      title: '上传您的照片',
      description: '上传清晰的正面照片，我们的AI将为您生成专业的婚纱照',
      icon: Icons.camera_alt,
      features: [
        '支持JPG、PNG格式',
        '智能人脸识别',
        '照片质量检测',
        '隐私安全保护',
      ],
    ),
    OnboardingPage(
      title: '选择您的风格',
      description: '从多种婚纱款式、背景和风景中选择，打造独特的婚纱照',
      icon: Icons.palette,
      features: [
        '经典传统风格',
        '现代时尚风格',
        '浪漫梦幻风格',
        '多样背景场景',
      ],
    ),
    OnboardingPage(
      title: 'AI智能生成',
      description: '先进的AI技术将为您生成高质量的婚纱照，保存到相册随时查看',
      icon: Icons.auto_awesome,
      features: [
        '专业级AI算法',
        '高清画质输出',
        '快速生成处理',
        '一键保存分享',
      ],
    ),
  ];
}
