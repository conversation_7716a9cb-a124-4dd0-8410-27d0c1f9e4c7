import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../core/router/app_router.dart';
import '../../../../../core/theme/wedding_colors.dart';
import '../../../../../core/theme/design_system/wedding_buttons.dart';
import '../../../../../core/services/first_launch_service.dart';
import '../../../../../core/di/injection.dart';
import '../../../../../shared/constants/app_constants.dart';

@RoutePage()
class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
  }

  void _startAnimations() async {
    await Future.delayed(const Duration(milliseconds: 300));
    _fadeController.forward();
    await Future.delayed(const Duration(milliseconds: 200));
    _scaleController.forward();
  }

  void _navigateToOnboarding() {
    context.router.replace(const OnboardingRoute());
  }

  void _navigateToMain() {
    // Check if user has completed onboarding
    final firstLaunchService = getIt<FirstLaunchService>();
    if (firstLaunchService.isOnboardingCompleted) {
      context.router.replace(const MainRoute());
    } else {
      // If not completed onboarding, go to onboarding first
      context.router.replace(const OnboardingRoute());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: WeddingColors.weddingGradient,
        ),
        child: SafeArea(
          child: AnimatedBuilder(
            animation: _fadeAnimation,
            builder: (context, child) {
              return Opacity(
                opacity: _fadeAnimation.value,
                child: Column(
                  children: [
                    // Logo and Title Section
                    Expanded(
                      flex: 3,
                      child: Container(
                        alignment: Alignment.center,
                        child: AnimatedBuilder(
                          animation: _scaleAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: _scaleAnimation.value,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  // Logo with enhanced design
                                  Container(
                                    width: 120.w,
                                    height: 120.w,
                                    decoration: BoxDecoration(
                                      color: WeddingColors.pureWhite.withOpacity(0.95),
                                      borderRadius: BorderRadius.circular(60.w),
                                      boxShadow: [
                                        BoxShadow(
                                          color: WeddingColors.shadowMedium,
                                          blurRadius: 25,
                                          offset: const Offset(0, 12),
                                          spreadRadius: 2,
                                        ),
                                        BoxShadow(
                                          color: WeddingColors.accentGold.withOpacity(0.3),
                                          blurRadius: 15,
                                          offset: const Offset(0, 6),
                                        ),
                                      ],
                                    ),
                                    child: Icon(
                                      Icons.favorite,
                                      size: 60.sp,
                                      color: WeddingColors.primaryPink,
                                    ),
                                  ),
                                  SizedBox(height: 32.h),
                                  // App Name with improved styling
                                  Text(
                                    'AI婚纱照',
                                    style: TextStyle(
                                      fontFamily: 'Dancing Script',
                                      fontSize: 42.sp,
                                      fontWeight: FontWeight.w600,
                                      color: WeddingColors.pureWhite,
                                      shadows: [
                                        Shadow(
                                          color: WeddingColors.shadowMedium,
                                          blurRadius: 10,
                                          offset: const Offset(0, 3),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ),

                    // Tagline Section
                    Expanded(
                      flex: 1,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Decorative hearts
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              _buildHeartIcon(size: 12.sp),
                              SizedBox(width: 16.w),
                              _buildHeartIcon(size: 16.sp),
                              SizedBox(width: 16.w),
                              _buildHeartIcon(size: 12.sp),
                            ],
                          ),
                          SizedBox(height: 24.h),
                          // Enhanced tagline
                          Text(
                            '用AI创造您的完美婚纱照',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: WeddingColors.pureWhite.withOpacity(0.9),
                              fontWeight: FontWeight.w400,
                              letterSpacing: 1.2,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    // Action Buttons Section
                    Expanded(
                      flex: 2,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Action buttons matching prototype
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 48.w),
                            child: Column(
                              children: [
                                // Primary button - Start Experience
                                WeddingButtons.primary(
                                  text: '开始体验',
                                  onPressed: _navigateToOnboarding,
                                  width: double.infinity,
                                  height: 56.h,
                                ),
                                SizedBox(height: 16.h),
                                // Secondary button - View Gallery
                                WeddingButtons.secondary(
                                  text: '查看作品',
                                  onPressed: _navigateToMain,
                                  width: double.infinity,
                                  height: 56.h,
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 40.h),
                          // Copyright info
                          Text(
                            '© 2024 Hera AI',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: WeddingColors.pureWhite.withOpacity(0.6),
                              fontWeight: FontWeight.w300,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildHeartIcon({required double size}) {
    return Icon(
      Icons.favorite,
      size: size,
      color: WeddingColors.pureWhite.withOpacity(0.6),
    );
  }
} 