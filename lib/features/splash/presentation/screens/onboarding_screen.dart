import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../core/router/app_router.dart';
import '../../../../../core/theme/wedding_colors.dart';
import '../../../../../core/theme/design_system/wedding_buttons.dart';
import '../../../../../core/services/first_launch_service.dart';
import '../../../../../core/di/injection.dart';
import '../../../../../shared/constants/app_constants.dart';
import '../../domain/entities/onboarding_page.dart';

@RoutePage()
class OnboardingScreen extends ConsumerStatefulWidget {
  const OnboardingScreen({super.key});

  @override
  ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends ConsumerState<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < OnboardingData.pages.length - 1) {
      _pageController.nextPage(
        duration: AppDurations.animation,
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: AppDurations.animation,
        curve: Curves.easeInOut,
      );
    }
  }

  void _completeOnboarding() async {
    // 标记引导完成
    final firstLaunchService = getIt<FirstLaunchService>();
    await firstLaunchService.markFirstLaunchCompleted();
    await firstLaunchService.markOnboardingCompleted();

    // 导航到主页面
    if (mounted) {
      context.router.replace(const MainRoute());
    }
  }

  void _skipOnboarding() {
    _completeOnboarding();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: WeddingColors.weddingGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 顶部跳过按钮
              Padding(
                padding: EdgeInsets.all(16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 返回按钮（仅在非第一页显示）
                    if (_currentPage > 0)
                      IconButton(
                        onPressed: _previousPage,
                        icon: Icon(
                          Icons.arrow_back_ios,
                          color: WeddingColors.pureWhite,
                          size: 20.sp,
                        ),
                      )
                    else
                      SizedBox(width: 48.w),
                    
                    // 页面指示器
                    Row(
                      children: List.generate(
                        OnboardingData.pages.length,
                        (index) => Container(
                          margin: EdgeInsets.symmetric(horizontal: 4.w),
                          width: _currentPage == index ? 24.w : 8.w,
                          height: 8.h,
                          decoration: BoxDecoration(
                            color: _currentPage == index
                                ? WeddingColors.pureWhite
                                : WeddingColors.pureWhite.withOpacity(0.4),
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                        ),
                      ),
                    ),
                    
                    // 跳过按钮
                    TextButton(
                      onPressed: _skipOnboarding,
                      child: Text(
                        '跳过',
                        style: TextStyle(
                          color: WeddingColors.pureWhite,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // 页面内容
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentPage = index;
                    });
                  },
                  itemCount: OnboardingData.pages.length,
                  itemBuilder: (context, index) {
                    final page = OnboardingData.pages[index];
                    return _buildPageContent(page);
                  },
                ),
              ),
              
              // 底部按钮区域
              Padding(
                padding: EdgeInsets.all(24.w),
                child: Column(
                  children: [
                    // 按钮行
                    Row(
                      children: [
                        // 上一步按钮（仅在非第一页显示）
                        if (_currentPage > 0) ...[
                          Expanded(
                            child: WeddingButtons.secondary(
                              text: '上一步',
                              onPressed: _previousPage,
                              height: 56.h,
                            ),
                          ),
                          SizedBox(width: 16.w),
                        ],
                        // 下一步/开始使用按钮
                        Expanded(
                          flex: _currentPage > 0 ? 1 : 1,
                          child: WeddingButtons.primary(
                            text: _currentPage == OnboardingData.pages.length - 1
                                ? '开始使用'
                                : '下一步',
                            onPressed: _nextPage,
                            height: 56.h,
                          ),
                        ),
                      ],
                    ),

                    // 服务条款提示（仅在最后一页显示）
                    if (_currentPage == OnboardingData.pages.length - 1) ...[
                      SizedBox(height: 16.h),
                      Text(
                        '点击开始使用即表示您同意我们的服务条款和隐私政策',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: WeddingColors.pureWhite.withOpacity(0.7),
                          height: 1.4,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPageContent(OnboardingPage page) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 32.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 增强的图标设计
          Container(
            width: 140.w,
            height: 140.w,
            decoration: BoxDecoration(
              color: WeddingColors.pureWhite.withOpacity(0.12),
              borderRadius: BorderRadius.circular(70.w),
              border: Border.all(
                color: WeddingColors.pureWhite.withOpacity(0.25),
                width: 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: WeddingColors.shadowMedium,
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Icon(
              page.icon,
              size: 70.sp,
              color: WeddingColors.pureWhite,
            ),
          ),

          SizedBox(height: 48.h),

          // 增强的标题
          Text(
            page.title,
            style: TextStyle(
              fontSize: 32.sp,
              fontWeight: FontWeight.bold,
              color: WeddingColors.pureWhite,
              height: 1.2,
              letterSpacing: 0.5,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 24.h),

          // 增强的描述
          Text(
            page.description,
            style: TextStyle(
              fontSize: 18.sp,
              color: WeddingColors.pureWhite.withOpacity(0.9),
              height: 1.6,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),

          // 功能列表（更好的设计）
          if (page.features != null) ...[
            SizedBox(height: 40.h),
            Container(
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: WeddingColors.pureWhite.withOpacity(0.08),
                borderRadius: BorderRadius.circular(16.r),
                border: Border.all(
                  color: WeddingColors.pureWhite.withOpacity(0.15),
                  width: 1,
                ),
              ),
              child: Column(
                children: page.features!.map((feature) => Padding(
                  padding: EdgeInsets.symmetric(vertical: 6.h),
                  child: Row(
                    children: [
                      Container(
                        width: 24.w,
                        height: 24.w,
                        decoration: BoxDecoration(
                          color: WeddingColors.accentGold.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12.w),
                        ),
                        child: Icon(
                          Icons.check,
                          color: WeddingColors.accentGold,
                          size: 16.sp,
                        ),
                      ),
                      SizedBox(width: 16.w),
                      Expanded(
                        child: Text(
                          feature,
                          style: TextStyle(
                            fontSize: 15.sp,
                            color: WeddingColors.pureWhite.withOpacity(0.85),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                )).toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
