# AI婚纱照生成API集成指南

## 概述

本应用集成了AI图片生成服务，使用DALL-E-3模型来生成高质量的婚纱照。本文档详细说明了API的集成方式、配置方法和使用示例。

## API配置

### 1. API端点信息

- **基础URL**: `https://api.hdgsb.com/v1`
- **图片生成端点**: `/images/generations`
- **图片分析端点**: `/chat/completions`
- **认证方式**: Bearer Token

### 2. 环境变量配置

在项目根目录的 `.env` 文件中配置API密钥：

```bash
# 主要API密钥（优先级最高）
HDGSB_API_KEY=your_actual_api_key_here

# 备用API密钥配置
AI_GENERATION_API_KEY=your_actual_api_key_here
OPENAI_API_KEY=your_actual_api_key_here
API_KEY=your_actual_api_key_here
```

### 3. 应用内配置

如果环境变量未配置，用户可以在应用内设置API密钥：

1. 启动应用
2. 如果API密钥未配置，会在右上角显示设置图标
3. 点击设置图标进入API配置页面
4. 输入API密钥并测试连接
5. 保存配置

## API使用流程

### 1. 图片分析阶段

```dart
// 单张图片分析
final analysis = await aiService.analyzeImageForWeddingPhoto(imageFile);

// 多张图片分析
final analysis = await aiService.analyzeMultipleImagesForWeddingPhoto(imageFiles);
```

**请求示例**:
```json
{
  "model": "gpt-4o",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请分析这张照片中的人物特征..."
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,{base64_image}",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 500,
  "temperature": 0.7
}
```

### 2. 图片生成阶段

```dart
// 使用分析结果生成婚纱照
final imageUrl = await aiService.generateWeddingPhotoWithDALLE(analysis);
```

**请求示例**:
```json
{
  "model": "dall-e-3",
  "prompt": "High-quality professional wedding photography...",
  "n": 1,
  "size": "1024x1024",
  "quality": "hd",
  "style": "vivid",
  "response_format": "url"
}
```

**响应示例**:
```json
{
  "data": [
    {
      "url": "https://example.com/generated-image.jpg"
    }
  ]
}
```

## 错误处理

### 常见错误码

| 状态码 | 错误类型 | 处理方式 |
|--------|----------|----------|
| 401 | API密钥无效 | 提示用户重新配置API密钥 |
| 429 | 请求频率过高 | 提示用户稍后重试 |
| 400 | 请求参数错误 | 检查输入内容格式 |
| 413 | 文件过大 | 提示用户选择较小的图片 |
| 500 | 服务器错误 | 提示用户稍后重试 |

### 错误处理示例

```dart
try {
  final result = await aiService.generateWeddingPhotos(images);
  if (result.success) {
    // 处理成功结果
  } else {
    // 处理业务错误
    showError(result.message);
  }
} on DioException catch (e) {
  // 处理网络错误
  if (e.response?.statusCode == 401) {
    showApiKeyError();
  } else if (e.response?.statusCode == 429) {
    showRateLimitError();
  } else {
    showNetworkError(e.message);
  }
} catch (e) {
  // 处理其他错误
  showGeneralError(e.toString());
}
```

## 安全最佳实践

### 1. API密钥管理

- ✅ **使用环境变量**: 在生产环境中通过环境变量配置API密钥
- ✅ **定期轮换**: 定期更换API密钥以确保安全
- ❌ **避免硬编码**: 不要在代码中硬编码API密钥
- ❌ **避免版本控制**: 不要将真实API密钥提交到Git

### 2. 请求安全

- ✅ **HTTPS通信**: 所有API请求都使用HTTPS
- ✅ **请求验证**: 验证请求参数的合法性
- ✅ **错误处理**: 妥善处理API错误响应
- ✅ **超时设置**: 设置合理的请求超时时间

### 3. 数据保护

- ✅ **图片压缩**: 上传前适当压缩图片以减少传输时间
- ✅ **临时存储**: 生成的图片URL及时缓存，避免重复请求
- ✅ **用户隐私**: 不保存用户上传的原始图片
- ✅ **数据清理**: 定期清理临时文件和缓存

## 性能优化

### 1. 请求优化

```dart
// 设置合理的超时时间
_dio.options = BaseOptions(
  connectTimeout: const Duration(seconds: 30),
  receiveTimeout: const Duration(seconds: 60),
  sendTimeout: const Duration(seconds: 60),
);

// 图片压缩
final compressedImage = await compressImage(originalImage);
```

### 2. 缓存策略

```dart
// 缓存生成结果
final cacheKey = generateCacheKey(images);
final cachedResult = await getCachedResult(cacheKey);
if (cachedResult != null) {
  return cachedResult;
}
```

### 3. 并发控制

```dart
// 限制并发请求数量
final semaphore = Semaphore(maxConcurrent: 3);
await semaphore.acquire();
try {
  final result = await apiCall();
  return result;
} finally {
  semaphore.release();
}
```

## 测试

### 1. 单元测试

```dart
test('should generate wedding photo successfully', () async {
  // Arrange
  final mockDio = MockDio();
  final service = AIGenerationService();
  
  // Act
  final result = await service.generateWeddingPhotos(testImages);
  
  // Assert
  expect(result.success, isTrue);
});
```

### 2. 集成测试

```dart
testWidgets('should show API config when key is missing', (tester) async {
  // Arrange
  ApiConfig.setApiKey('');
  
  // Act
  await tester.pumpWidget(MyApp());
  
  // Assert
  expect(find.byIcon(Icons.settings), findsOneWidget);
});
```

## 监控和日志

### 1. 日志记录

```dart
// 记录API调用
_logger.i('开始生成婚纱照，图片数量: ${images.length}');
_logger.d('API请求: POST /images/generations');
_logger.i('生成成功，耗时: ${duration}ms');
```

### 2. 性能监控

```dart
// 监控API响应时间
final stopwatch = Stopwatch()..start();
final result = await apiCall();
stopwatch.stop();
_logger.d('API响应时间: ${stopwatch.elapsedMilliseconds}ms');
```

### 3. 错误追踪

```dart
// 记录错误详情
_logger.e('API调用失败', error, stackTrace);
```

## 故障排除

### 常见问题

1. **API密钥无效**
   - 检查密钥格式是否正确
   - 确认密钥是否有效且未过期
   - 验证密钥权限是否足够

2. **网络连接问题**
   - 检查网络连接状态
   - 确认防火墙设置
   - 尝试使用不同的网络环境

3. **图片上传失败**
   - 检查图片格式是否支持
   - 确认图片大小是否超限
   - 验证图片文件是否损坏

4. **生成结果异常**
   - 检查输入图片质量
   - 确认提示词是否合适
   - 尝试调整生成参数

### 调试技巧

1. **启用详细日志**
   ```dart
   LogService.setLevel(LogLevel.debug);
   ```

2. **使用网络抓包工具**
   - Charles Proxy
   - Wireshark
   - Flutter Inspector

3. **API测试工具**
   - Postman
   - curl命令
   - 在线API测试工具

## 更新日志

### v1.0.0 (2024-01-15)
- ✅ 初始API集成
- ✅ 基础错误处理
- ✅ 环境变量配置支持

### v1.1.0 (计划中)
- 🔄 批量生成支持
- 🔄 更多风格模板
- 🔄 生成进度显示
- 🔄 结果缓存优化
