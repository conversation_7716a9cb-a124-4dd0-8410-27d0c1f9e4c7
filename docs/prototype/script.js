// 全局变量
let currentScreen = 'splash-screen';
let currentStep = 1;
let selectedStyle = null;
let selectedBackground = null;
let uploadedImage = null;
let generatedPhotos = [];

// 会员相关变量
let isPremiumUser = false;
let dailyGenerationCount = 0;
let maxDailyGeneration = 3;
let membershipExpiry = null;
let selectedPlan = null;

// 用户登录相关变量
let isLoggedIn = false;
let userInfo = {
    id: null,
    name: '游客用户',
    avatar: null,
    loginMethod: null
};

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    checkPermissions();
});

// 初始化应用
function initializeApp() {
    // 加载保存的数据
    loadGalleryData();
    loadMembershipData();
    loadUserData();
    
    // 设置初始状态
    updateGenerateButton();
    updateMembershipUI();
    updateUserUI();
    
    // 添加动画类
    document.body.classList.add('fade-in');
    
    // 设置点击外部关闭菜单
    document.addEventListener('click', handleOutsideClick);
}

// 设置事件监听器
function setupEventListeners() {
    // 风格选择
    document.querySelectorAll('.style-card').forEach(card => {
        card.addEventListener('click', function() {
            if (this.classList.contains('premium-locked') && !isPremiumUser) {
                showUpgradeModal('此风格需要会员权限');
            } else {
                selectStyle(this);
            }
        });
    });
    
    // 背景选择
    document.querySelectorAll('.background-card').forEach(card => {
        card.addEventListener('click', function() {
            if (this.classList.contains('premium-locked') && !isPremiumUser) {
                showUpgradeModal('此背景需要会员权限');
            } else {
                selectBackground(this);
            }
        });
    });
    
    // 上传区域点击
    document.getElementById('uploadArea').addEventListener('click', function() {
        triggerFileUpload();
    });
    
    // 拖拽上传
    const uploadArea = document.getElementById('uploadArea');
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('drop', handleDrop);
}

// 屏幕切换
function showScreen(screenId) {
    // 隐藏当前屏幕
    document.querySelectorAll('.screen').forEach(screen => {
        screen.classList.remove('active');
    });
    
    // 显示目标屏幕
    document.getElementById(screenId).classList.add('active');
    currentScreen = screenId;
    
    // 特殊处理
    if (screenId === 'gallery') {
        updateGallery();
    }
}

// 引导页面步骤控制
function nextStep() {
    if (currentStep < 3) {
        currentStep++;
        updateOnboardingStep();
    } else {
        showScreen('main');
    }
}

function prevStep() {
    if (currentStep > 1) {
        currentStep--;
        updateOnboardingStep();
    } else {
        showScreen('splash-screen');
    }
}

function updateOnboardingStep() {
    // 更新进度点
    document.querySelectorAll('.dot').forEach((dot, index) => {
        if (index < currentStep) {
            dot.classList.add('active');
        } else {
            dot.classList.remove('active');
        }
    });
    
    // 更新步骤内容
    document.querySelectorAll('.onboarding-step').forEach((step, index) => {
        if (index + 1 === currentStep) {
            step.classList.add('active');
        } else {
            step.classList.remove('active');
        }
    });
    
    // 更新按钮状态
    const prevBtn = document.querySelector('.onboarding-footer .btn-secondary');
    const nextBtn = document.querySelector('.onboarding-footer .btn-primary');
    
    if (currentStep === 1) {
        prevBtn.textContent = '返回';
    } else {
        prevBtn.textContent = '上一步';
    }
    
    if (currentStep === 3) {
        nextBtn.textContent = '开始使用';
    } else {
        nextBtn.textContent = '下一步';
    }
}

// 文件上传处理
function triggerFileUpload() {
    document.getElementById('fileInput').click();
}

function handleFileUpload(event) {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(e) {
            uploadedImage = e.target.result;
            showUploadedPhoto(e.target.result);
            updateGenerateButton();
            showToast('照片上传成功！', 'success');
        };
        reader.readAsDataURL(file);
    } else {
        showToast('请选择有效的图片文件', 'error');
    }
}

function showUploadedPhoto(imageSrc) {
    document.getElementById('uploadArea').style.display = 'none';
    const uploadedPhoto = document.getElementById('uploadedPhoto');
    const previewImage = document.getElementById('previewImage');
    
    previewImage.src = imageSrc;
    uploadedPhoto.style.display = 'block';
}

function removePhoto() {
    uploadedImage = null;
    document.getElementById('uploadArea').style.display = 'block';
    document.getElementById('uploadedPhoto').style.display = 'none';
    document.getElementById('fileInput').value = '';
    updateGenerateButton();
}

// 拖拽上传处理
function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.style.borderColor = '#667eea';
    e.currentTarget.style.background = 'rgba(102, 126, 234, 0.1)';
}

function handleDrop(e) {
    e.preventDefault();
    e.currentTarget.style.borderColor = '#cbd5e0';
    e.currentTarget.style.background = 'transparent';
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                uploadedImage = e.target.result;
                showUploadedPhoto(e.target.result);
                updateGenerateButton();
                showToast('照片上传成功！', 'success');
            };
            reader.readAsDataURL(file);
        }
    }
}

// 风格选择
function selectStyle(element) {
    // 移除其他选中状态
    document.querySelectorAll('.style-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // 添加选中状态
    element.classList.add('selected');
    selectedStyle = element.dataset.style;
    
    updateGenerateButton();
    showToast('风格选择成功', 'success');
}

// 背景选择
function selectBackground(element) {
    // 移除其他选中状态
    document.querySelectorAll('.background-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // 添加选中状态
    element.classList.add('selected');
    selectedBackground = element.dataset.bg;
    
    updateGenerateButton();
    showToast('背景选择成功', 'success');
}

// 更新生成按钮状态
function updateGenerateButton() {
    const generateBtn = document.getElementById('generateBtn');
    const canGenerate = uploadedImage && selectedStyle && selectedBackground;
    
    generateBtn.disabled = !canGenerate;
    
    if (canGenerate) {
        generateBtn.innerHTML = '<i class="fas fa-magic"></i> 生成AI婚纱照';
    } else {
        generateBtn.innerHTML = '<i class="fas fa-magic"></i> 请完成所有选择';
    }
}

// 生成照片
function generatePhoto() {
    if (!uploadedImage || !selectedStyle || !selectedBackground) {
        showToast('请完成所有必要的选择', 'error');
        return;
    }
    
    // 检查免费用户的生成次数限制
    if (!isPremiumUser && dailyGenerationCount >= maxDailyGeneration) {
        showUpgradeModal('今日免费生成次数已用完，升级会员即可无限生成');
        return;
    }
    
    showScreen('generating');
    simulateGeneration();
}

// 模拟生成过程
function simulateGeneration() {
    let progress = 0;
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 100) progress = 100;
        
        progressFill.style.width = progress + '%';
        progressText.textContent = Math.round(progress) + '%';
        
        if (progress >= 100) {
            clearInterval(interval);
            setTimeout(() => {
                showGenerationResult();
            }, 500);
        }
    }, 300);
}

// 显示生成结果（已在下方重新定义）

// 获取风格名称
function getStyleName(style) {
    const styleNames = {
        'classic': '经典款',
        'modern': '现代款',
        'princess': '公主款',
        'vintage': '复古款'
    };
    return styleNames[style] || '未知';
}

// 获取背景名称
function getBackgroundName(bg) {
    const bgNames = {
        'beach': '海滩',
        'garden': '花园',
        'church': '教堂',
        'mountain': '山景'
    };
    return bgNames[bg] || '未知';
}

// 保存到相册（已在下方重新定义）

// 重新生成
function regeneratePhoto() {
    showScreen('generating');
    simulateGeneration();
}

// 分享照片
function sharePhoto() {
    if (navigator.share) {
        navigator.share({
            title: 'AI生成的婚纱照',
            text: '看看我的AI婚纱照！',
            url: window.location.href
        }).catch(console.error);
    } else {
        // 降级处理：复制链接
        navigator.clipboard.writeText(window.location.href).then(() => {
            showToast('链接已复制到剪贴板', 'success');
        }).catch(() => {
            showToast('分享功能暂不可用', 'error');
        });
    }
}

// 相册管理
function updateGallery() {
    const galleryGrid = document.getElementById('galleryGrid');
    const emptyGallery = document.getElementById('emptyGallery');
    
    if (generatedPhotos.length === 0) {
        galleryGrid.style.display = 'none';
        emptyGallery.style.display = 'block';
    } else {
        galleryGrid.style.display = 'grid';
        emptyGallery.style.display = 'none';
        
        // 清空现有内容
        galleryGrid.innerHTML = '';
        
        // 添加照片
        generatedPhotos.forEach(photo => {
            const galleryItem = createGalleryItem(photo);
            galleryGrid.appendChild(galleryItem);
        });
    }
}

function createGalleryItem(photo) {
    const item = document.createElement('div');
    item.className = 'gallery-item';
    item.innerHTML = `
        <img src="${photo.image}" alt="生成的婚纱照">
        <div class="gallery-overlay">
            <button class="btn-icon" onclick="viewPhoto('${photo.id}')">
                <i class="fas fa-eye"></i>
            </button>
            <button class="btn-icon" onclick="deletePhoto('${photo.id}')">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    return item;
}

function viewPhoto(photoId) {
    const photo = generatedPhotos.find(p => p.id == photoId);
    if (photo) {
        // 显示大图查看
        showPhotoModal(photo);
    }
}

function deletePhoto(photoId) {
    if (confirm('确定要删除这张照片吗？')) {
        generatedPhotos = generatedPhotos.filter(p => p.id != photoId);
        saveGalleryData();
        updateGallery();
        showToast('照片已删除', 'success');
    }
}

function clearGallery() {
    if (confirm('确定要清空所有照片吗？此操作不可恢复。')) {
        generatedPhotos = [];
        saveGalleryData();
        updateGallery();
        showToast('相册已清空', 'success');
    }
}

// 数据持久化
function saveGalleryData() {
    localStorage.setItem('aiWeddingPhotos', JSON.stringify(generatedPhotos));
}

function loadGalleryData() {
    const saved = localStorage.getItem('aiWeddingPhotos');
    if (saved) {
        generatedPhotos = JSON.parse(saved);
    }
}

// 权限管理
function checkPermissions() {
    // 检查相机权限
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(stream => {
                stream.getTracks().forEach(track => track.stop());
            })
            .catch(() => {
                // 权限被拒绝，显示权限请求弹窗
                setTimeout(() => {
                    showPermissionModal();
                }, 2000);
            });
    }
}

function showPermissionModal() {
    const modal = document.getElementById('permissionModal');
    modal.classList.add('show');
}

function closeModal() {
    const modal = document.getElementById('permissionModal');
    modal.classList.remove('show');
}

function requestPermissions() {
    // 请求相机权限
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(stream => {
                stream.getTracks().forEach(track => track.stop());
                showToast('相机权限已获取', 'success');
                closeModal();
            })
            .catch(() => {
                showToast('请在设置中允许相机权限', 'error');
            });
    }
}

function requestPhotoLibraryPermission() {
    // 在实际iOS应用中，这里会调用原生API请求相册权限
    // 这里只是模拟
    showToast('正在保存到设备相册...', 'success');
}

// Toast 提示
function showToast(message, type = 'info') {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.className = `toast ${type} show`;
    
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// 设置页面功能
function showAbout() {
    showToast('关于页面开发中...', 'info');
}

function showPrivacy() {
    showToast('隐私政策页面开发中...', 'info');
}

// 照片查看模态框
function showPhotoModal(photo) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>照片详情</h3>
            </div>
            <div class="modal-body">
                <img src="${photo.image}" alt="照片" style="width: 100%; border-radius: 8px; margin-bottom: 1rem;">
                <div class="info-item">
                    <span class="label">风格：</span>
                    <span class="value">${getStyleName(photo.style)}</span>
                </div>
                <div class="info-item">
                    <span class="label">背景：</span>
                    <span class="value">${getBackgroundName(photo.background)}</span>
                </div>
                <div class="info-item">
                    <span class="label">创建时间：</span>
                    <span class="value">${new Date(photo.timestamp).toLocaleString()}</span>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closePhotoModal()">关闭</button>
                <button class="btn-primary" onclick="savePhotoToDevice('${photo.id}')">保存到设备</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 点击背景关闭
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closePhotoModal();
        }
    });
}

function closePhotoModal() {
    const modal = document.querySelector('.modal.show');
    if (modal && modal.querySelector('img')) {
        modal.remove();
    }
}

function savePhotoToDevice(photoId) {
    showToast('正在保存到设备...', 'success');
    closePhotoModal();
}

// 键盘事件处理
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        // ESC键关闭模态框
        const modal = document.querySelector('.modal.show');
        if (modal) {
            if (modal.id === 'permissionModal') {
                closeModal();
            } else {
                closePhotoModal();
            }
        }
    }
});

// 防止页面刷新时丢失状态
window.addEventListener('beforeunload', function() {
    saveGalleryData();
});

// 处理页面可见性变化
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // 页面隐藏时保存数据
        saveGalleryData();
    }
});

// 添加一些示例数据（仅用于演示）
function addSampleData() {
    if (generatedPhotos.length === 0) {
        generatedPhotos = [
            {
                id: 1,
                image: 'https://via.placeholder.com/300x400/f8f9fa/6c757d?text=示例作品1',
                style: 'classic',
                background: 'beach',
                timestamp: new Date(Date.now() - 86400000).toISOString()
            },
            {
                id: 2,
                image: 'https://via.placeholder.com/300x400/f8f9fa/6c757d?text=示例作品2',
                style: 'modern',
                background: 'garden',
                timestamp: new Date(Date.now() - 172800000).toISOString()
            }
        ];
        saveGalleryData();
    }
}

// 会员订阅相关功能
function loadMembershipData() {
    const membershipData = localStorage.getItem('membershipData');
    if (membershipData) {
        const data = JSON.parse(membershipData);
        isPremiumUser = data.isPremium || false;
        dailyGenerationCount = data.dailyCount || 0;
        membershipExpiry = data.expiry || null;
        
        // 检查会员是否过期
        if (isPremiumUser && membershipExpiry && new Date(membershipExpiry) < new Date()) {
            isPremiumUser = false;
            membershipExpiry = null;
            saveMembershipData();
        }
        
        // 重置每日计数（简化处理，实际应用中需要检查日期）
        const lastResetDate = data.lastResetDate || '';
        const today = new Date().toDateString();
        if (lastResetDate !== today) {
            dailyGenerationCount = 0;
            saveMembershipData();
        }
    }
}

function saveMembershipData() {
    const membershipData = {
        isPremium: isPremiumUser,
        dailyCount: dailyGenerationCount,
        expiry: membershipExpiry,
        lastResetDate: new Date().toDateString()
    };
    localStorage.setItem('membershipData', JSON.stringify(membershipData));
}

function updateMembershipUI() {
    // 更新会员状态提示条
    const membershipBanner = document.getElementById('membershipBanner');
    const bannerText = document.getElementById('bannerText');
    const membershipBtn = document.getElementById('membershipBtn');
    
    if (isPremiumUser) {
        membershipBanner.style.display = 'none';
        membershipBtn.style.color = '#ff6b9d';
    } else {
        membershipBanner.style.display = 'flex';
        bannerText.textContent = `今日剩余生成次数：${maxDailyGeneration - dailyGenerationCount}次`;
        membershipBtn.style.color = '#667eea';
    }
    
    // 更新设置页面的会员状态
    const membershipStatusText = document.getElementById('membershipStatusText');
    if (membershipStatusText) {
        if (isPremiumUser) {
            membershipStatusText.textContent = `尊享会员 - 有效期至${new Date(membershipExpiry).toLocaleDateString()}`;
        } else {
            membershipStatusText.textContent = `免费用户 - 今日剩余${maxDailyGeneration - dailyGenerationCount}次生成`;
        }
    }
    
    // 更新订阅页面的会员状态
    updateSubscriptionPageUI();
}

function updateSubscriptionPageUI() {
    const statusFree = document.querySelector('.status-free');
    const statusPremium = document.querySelector('.status-premium');
    const remainingCount = document.getElementById('remainingCount');
    const expiryDate = document.getElementById('expiryDate');
    
    if (isPremiumUser) {
        statusFree.style.display = 'none';
        statusPremium.style.display = 'flex';
        if (expiryDate) {
            expiryDate.textContent = new Date(membershipExpiry).toLocaleDateString();
        }
    } else {
        statusFree.style.display = 'flex';
        statusPremium.style.display = 'none';
        if (remainingCount) {
            remainingCount.textContent = maxDailyGeneration - dailyGenerationCount;
        }
    }
}

function showSubscriptionPlans() {
    const subscriptionPlans = document.getElementById('subscriptionPlans');
    subscriptionPlans.style.display = 'block';
    subscriptionPlans.scrollIntoView({ behavior: 'smooth' });
}

function selectPlan(planType) {
    selectedPlan = planType;
    
    // 更新选中状态
    document.querySelectorAll('.plan-card').forEach(card => {
        card.classList.remove('selected');
    });
    document.querySelector(`[data-plan="${planType}"]`).classList.add('selected');
    
    // 显示订阅确认弹窗
    showSubscriptionModal(planType);
}

function showSubscriptionModal(planType) {
    const modal = document.getElementById('subscriptionModal');
    const planName = document.getElementById('selectedPlanName');
    const planPrice = document.getElementById('selectedPlanPrice');
    
    const planInfo = {
        'monthly': { name: '月度会员', price: '¥29.9/月' },
        'quarterly': { name: '季度会员', price: '¥79.9/季' },
        'yearly': { name: '年度会员', price: '¥299.9/年' }
    };
    
    planName.textContent = planInfo[planType].name;
    planPrice.textContent = planInfo[planType].price;
    
    modal.classList.add('show');
}

function closeSubscriptionModal() {
    const modal = document.getElementById('subscriptionModal');
    modal.classList.remove('show');
}

function confirmSubscription() {
    // 模拟订阅成功
    isPremiumUser = true;
    
    // 设置会员到期时间
    const now = new Date();
    switch (selectedPlan) {
        case 'monthly':
            membershipExpiry = new Date(now.setMonth(now.getMonth() + 1)).toISOString();
            break;
        case 'quarterly':
            membershipExpiry = new Date(now.setMonth(now.getMonth() + 3)).toISOString();
            break;
        case 'yearly':
            membershipExpiry = new Date(now.setFullYear(now.getFullYear() + 1)).toISOString();
            break;
    }
    
    saveMembershipData();
    updateMembershipUI();
    closeSubscriptionModal();
    
    showToast('订阅成功！欢迎成为尊享会员', 'success');
    
    // 返回主页面
    setTimeout(() => {
        showScreen('main');
    }, 1500);
}

function showUpgradeModal(message) {
    const modal = document.getElementById('upgradeModal');
    const upgradeMessage = document.getElementById('upgradeMessage');
    
    upgradeMessage.textContent = message;
    modal.classList.add('show');
}

function closeUpgradeModal() {
    const modal = document.getElementById('upgradeModal');
    modal.classList.remove('show');
}

function goToSubscription() {
    closeUpgradeModal();
    showScreen('subscription');
}

function toggleStyleView() {
    const premiumStyles = document.getElementById('premiumStyles');
    const toggleBtn = document.getElementById('styleToggle');
    
    if (premiumStyles.style.display === 'none') {
        premiumStyles.style.display = 'grid';
        toggleBtn.innerHTML = '<i class="fas fa-th"></i> 收起';
    } else {
        premiumStyles.style.display = 'none';
        toggleBtn.innerHTML = '<i class="fas fa-th-large"></i> 查看更多';
    }
}

function toggleBackgroundView() {
    const premiumBackgrounds = document.getElementById('premiumBackgrounds');
    const toggleBtn = document.getElementById('backgroundToggle');
    
    if (premiumBackgrounds.style.display === 'none') {
        premiumBackgrounds.style.display = 'grid';
        toggleBtn.innerHTML = '<i class="fas fa-th"></i> 收起';
    } else {
        premiumBackgrounds.style.display = 'none';
        toggleBtn.innerHTML = '<i class="fas fa-th-large"></i> 查看更多';
    }
}

function restorePurchases() {
    // 模拟恢复购买
    showToast('正在恢复购买记录...', 'info');
    
    setTimeout(() => {
        showToast('未找到可恢复的购买记录', 'info');
    }, 2000);
}

function manageMembership() {
    // 跳转到会员管理页面
    showToast('会员管理功能开发中...', 'info');
}

function showTerms() {
    showToast('服务条款页面开发中...', 'info');
}

// 更新生成结果保存逻辑
function saveToGallery() {
    const photo = {
        id: Date.now(),
        image: document.getElementById('resultImage').src,
        style: selectedStyle,
        background: selectedBackground,
        timestamp: new Date().toISOString(),
        isPremium: isPremiumUser
    };
    
    generatedPhotos.push(photo);
    saveGalleryData();
    
    // 增加生成计数（仅免费用户）
    if (!isPremiumUser) {
        dailyGenerationCount++;
        saveMembershipData();
        updateMembershipUI();
    }
    
    showToast('照片已保存到相册', 'success');
    
    // 请求保存到设备相册的权限
    requestPhotoLibraryPermission();
}

// 更新生成完成逻辑
function showGenerationResult() {
    // 模拟生成的图片
    const resultImage = document.getElementById('resultImage');
    
    // 根据会员状态添加水印
    if (isPremiumUser) {
        resultImage.src = 'https://via.placeholder.com/300x400/f8f9fa/6c757d?text=高清无水印婚纱照';
    } else {
        resultImage.src = 'https://via.placeholder.com/300x400/f8f9fa/6c757d?text=标清带水印婚纱照';
    }
    
    // 更新生成信息
    document.getElementById('selectedStyle').textContent = getStyleName(selectedStyle);
    document.getElementById('selectedBackground').textContent = getBackgroundName(selectedBackground);
    document.getElementById('generateTime').textContent = isPremiumUser ? '30秒' : '45秒';
    
    showScreen('result');
}

// 用户登录相关功能
function loadUserData() {
    const userData = localStorage.getItem('userData');
    if (userData) {
        const data = JSON.parse(userData);
        isLoggedIn = data.isLoggedIn || false;
        userInfo = { ...userInfo, ...data.userInfo };
    }
}

function saveUserData() {
    const userData = {
        isLoggedIn: isLoggedIn,
        userInfo: userInfo
    };
    localStorage.setItem('userData', JSON.stringify(userData));
}

function updateUserUI() {
    const userDisplayName = document.getElementById('userDisplayName');
    const userStatusText = document.getElementById('userStatusText');
    const loginMenuItem = document.getElementById('loginMenuItem');
    const cloudSyncItem = document.getElementById('cloudSyncItem');
    
    if (isLoggedIn) {
        userDisplayName.textContent = userInfo.name;
        userStatusText.textContent = '已登录 · 云端同步';
        loginMenuItem.style.display = 'none';
        cloudSyncItem.style.display = 'flex';
    } else {
        userDisplayName.textContent = '游客用户';
        userStatusText.textContent = '本地使用模式';
        loginMenuItem.style.display = 'flex';
        cloudSyncItem.style.display = 'none';
    }
}

function toggleUserMenu() {
    const userMenu = document.getElementById('userMenu');
    if (userMenu.style.display === 'none' || userMenu.style.display === '') {
        userMenu.style.display = 'block';
    } else {
        userMenu.style.display = 'none';
    }
}

function handleOutsideClick(event) {
    const userMenu = document.getElementById('userMenu');
    const userBtn = document.getElementById('userBtn');
    
    if (!userMenu.contains(event.target) && !userBtn.contains(event.target)) {
        userMenu.style.display = 'none';
    }
}

function showLoginPrompt() {
    toggleUserMenu(); // 关闭用户菜单
    const modal = document.getElementById('loginPromptModal');
    modal.classList.add('show');
}

function closeLoginPrompt() {
    const modal = document.getElementById('loginPromptModal');
    modal.classList.remove('show');
}

function showLoginOptions() {
    closeLoginPrompt();
    const modal = document.getElementById('loginOptionsModal');
    modal.classList.add('show');
}

function closeLoginOptions() {
    const modal = document.getElementById('loginOptionsModal');
    modal.classList.remove('show');
}

function loginWithApple() {
    // 模拟Apple ID登录
    simulateLogin('Apple ID', 'Apple用户');
}

function loginWithWechat() {
    // 模拟微信登录
    simulateLogin('微信', '微信用户');
}

function loginWithPhone() {
    // 模拟手机号登录
    simulateLogin('手机号', '手机用户');
}

function simulateLogin(method, name) {
    closeLoginOptions();
    
    // 显示登录中状态
    showToast('正在登录...', 'info');
    
    setTimeout(() => {
        // 模拟登录成功
        isLoggedIn = true;
        userInfo.name = name;
        userInfo.loginMethod = method;
        userInfo.id = 'user_' + Date.now();
        
        saveUserData();
        updateUserUI();
        
        showToast(`${method}登录成功！`, 'success');
        
        // 如果有本地作品，提示同步
        if (generatedPhotos.length > 0) {
            setTimeout(() => {
                showToast('正在同步本地作品到云端...', 'info');
                setTimeout(() => {
                    showToast('作品同步完成！', 'success');
                }, 2000);
            }, 1000);
        }
    }, 1500);
}

function showCloudSync() {
    toggleUserMenu(); // 关闭用户菜单
    
    // 更新同步信息
    const localPhotoCount = document.getElementById('localPhotoCount');
    const cloudPhotoCount = document.getElementById('cloudPhotoCount');
    const lastSyncTime = document.getElementById('lastSyncTime');
    
    localPhotoCount.textContent = `${generatedPhotos.length}张`;
    cloudPhotoCount.textContent = `${generatedPhotos.length}张`;
    lastSyncTime.textContent = '刚刚';
    
    const modal = document.getElementById('cloudSyncModal');
    modal.classList.add('show');
}

function closeCloudSyncModal() {
    const modal = document.getElementById('cloudSyncModal');
    modal.classList.remove('show');
}

function forceSyncNow() {
    showToast('正在同步...', 'info');
    
    setTimeout(() => {
        showToast('同步完成！', 'success');
        
        // 更新同步时间
        const lastSyncTime = document.getElementById('lastSyncTime');
        lastSyncTime.textContent = '刚刚';
    }, 2000);
}

// 更新保存到相册功能，添加登录提示
function saveToGallery() {
    const photo = {
        id: Date.now(),
        image: document.getElementById('resultImage').src,
        style: selectedStyle,
        background: selectedBackground,
        timestamp: new Date().toISOString(),
        isPremium: isPremiumUser,
        isLocal: !isLoggedIn
    };
    
    generatedPhotos.push(photo);
    saveGalleryData();
    
    // 增加生成计数（仅免费用户）
    if (!isPremiumUser) {
        dailyGenerationCount++;
        saveMembershipData();
        updateMembershipUI();
    }
    
    showToast('照片已保存到相册', 'success');
    
    // 如果未登录且作品较多，提示登录同步
    if (!isLoggedIn && generatedPhotos.length >= 3) {
        setTimeout(() => {
            showToast('登录账户可将作品同步到云端，永不丢失', 'info');
        }, 2000);
    }
    
    // 请求保存到设备相册的权限
    requestPhotoLibraryPermission();
}

// 在开发环境中添加示例数据
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    setTimeout(addSampleData, 1000);
}