<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI婚纱照生成器</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- 启动页面 -->
    <div id="splash-screen" class="screen active">
        <div class="splash-content">
            <div class="logo">
                <i class="fas fa-heart"></i>
                <h1>AI婚纱照</h1>
            </div>
            <p class="tagline">用AI创造您的完美婚纱照</p>
            <div class="splash-buttons">
                <button class="btn-primary" onclick="showScreen('onboarding')">开始体验</button>
                <button class="btn-secondary" onclick="showScreen('gallery')">查看作品</button>
            </div>
        </div>
    </div>

    <!-- 引导页面 -->
    <div id="onboarding" class="screen">
        <div class="onboarding-container">
            <div class="onboarding-header">
                <button class="btn-back" onclick="showScreen('splash-screen')">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <div class="progress-dots">
                    <span class="dot active"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                </div>
            </div>
            
            <div class="onboarding-content">
                <div class="onboarding-step active" data-step="1">
                    <div class="step-icon">
                        <i class="fas fa-camera"></i>
                    </div>
                    <h2>上传您的照片</h2>
                    <p>上传清晰的正面照片，我们的AI将为您生成专业的婚纱照</p>
                </div>
                
                <div class="onboarding-step" data-step="2">
                    <div class="step-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h2>选择您的风格</h2>
                    <p>从多种婚纱款式、背景和风景中选择，打造独特的婚纱照</p>
                </div>
                
                <div class="onboarding-step" data-step="3">
                    <div class="step-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h2>AI智能生成</h2>
                    <p>先进的AI技术将为您生成高质量的婚纱照，保存到相册随时查看</p>
                </div>
            </div>
            
            <div class="onboarding-footer">
                <button class="btn-secondary" onclick="prevStep()">上一步</button>
                <button class="btn-primary" onclick="nextStep()">下一步</button>
            </div>
        </div>
    </div>

    <!-- 主界面 -->
    <div id="main" class="screen">
        <header class="main-header">
            <div class="header-left">
                <h1>AI婚纱照</h1>
            </div>
            <div class="header-right">
                <button class="btn-icon" onclick="showScreen('subscription')" id="membershipBtn">
                    <i class="fas fa-crown"></i>
                </button>
                <button class="btn-icon" onclick="toggleUserMenu()" id="userBtn">
                    <i class="fas fa-user-circle"></i>
                </button>
                <button class="btn-icon" onclick="showScreen('gallery')">
                    <i class="fas fa-images"></i>
                </button>
            </div>
        </header>

        <!-- 用户菜单下拉 -->
        <div class="user-menu" id="userMenu" style="display: none;">
            <div class="user-menu-content">
                <div class="user-status" id="userStatus">
                    <div class="user-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="user-info">
                        <h4 id="userDisplayName">游客用户</h4>
                        <p id="userStatusText">本地使用模式</p>
                    </div>
                </div>
                
                <div class="menu-divider"></div>
                
                <div class="menu-items">
                    <div class="menu-item" onclick="showLoginPrompt()" id="loginMenuItem">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>登录账户</span>
                        <div class="menu-badge">同步数据</div>
                    </div>
                    
                    <div class="menu-item" onclick="showScreen('subscription')">
                        <i class="fas fa-crown"></i>
                        <span>会员订阅</span>
                    </div>
                    
                    <div class="menu-item" onclick="showScreen('settings')">
                        <i class="fas fa-cog"></i>
                        <span>设置</span>
                    </div>
                    
                    <div class="menu-item" onclick="showCloudSync()" id="cloudSyncItem" style="display: none;">
                        <i class="fas fa-cloud"></i>
                        <span>云端同步</span>
                        <div class="sync-status">
                            <i class="fas fa-check-circle text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 会员状态提示条 -->
        <div class="membership-banner" id="membershipBanner">
            <div class="banner-content">
                <div class="banner-info">
                    <i class="fas fa-info-circle"></i>
                    <span id="bannerText">今日剩余生成次数：3次</span>
                </div>
                <button class="btn-banner" onclick="showScreen('subscription')">
                    <i class="fas fa-crown"></i>
                    升级会员
                </button>
            </div>
        </div>

        <main class="main-content">
            <!-- 照片上传区域 -->
            <section class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-placeholder">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <h3>上传您的照片</h3>
                        <p>支持 JPG、PNG 格式，建议使用清晰的正面照片</p>
                        <button class="btn-upload" onclick="triggerFileUpload()">
                            <i class="fas fa-camera"></i>
                            选择照片
                        </button>
                    </div>
                    <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="handleFileUpload(event)">
                </div>
                
                <div class="uploaded-photo" id="uploadedPhoto" style="display: none;">
                    <img id="previewImage" src="" alt="预览图片">
                    <div class="photo-actions">
                        <button class="btn-secondary" onclick="removePhoto()">
                            <i class="fas fa-trash"></i>
                            重新选择
                        </button>
                    </div>
                </div>
            </section>

            <!-- 风格选择区域 -->
            <section class="style-section">
                <div class="section-header">
                    <h2>选择婚纱风格</h2>
                    <button class="btn-toggle" onclick="toggleStyleView()" id="styleToggle">
                        <i class="fas fa-th-large"></i>
                        查看更多
                    </button>
                </div>
                
                <!-- 基础风格 -->
                <div class="style-grid basic-styles">
                    <div class="style-card" data-style="classic">
                        <img src="https://via.placeholder.com/150x200/f8f9fa/6c757d?text=经典款" alt="经典款">
                        <h4>经典款</h4>
                        <p>优雅传统</p>
                    </div>
                    <div class="style-card" data-style="modern">
                        <img src="https://via.placeholder.com/150x200/f8f9fa/6c757d?text=现代款" alt="现代款">
                        <h4>现代款</h4>
                        <p>简约时尚</p>
                    </div>
                    <div class="style-card" data-style="princess">
                        <img src="https://via.placeholder.com/150x200/f8f9fa/6c757d?text=公主款" alt="公主款">
                        <h4>公主款</h4>
                        <p>梦幻浪漫</p>
                    </div>
                    <div class="style-card" data-style="vintage">
                        <img src="https://via.placeholder.com/150x200/f8f9fa/6c757d?text=复古款" alt="复古款">
                        <h4>复古款</h4>
                        <p>怀旧典雅</p>
                    </div>
                </div>
                
                <!-- 高级风格（会员专享） -->
                <div class="style-grid premium-styles" id="premiumStyles" style="display: none;">
                    <div class="style-card premium-locked" data-style="bohemian">
                        <img src="https://via.placeholder.com/150x200/f8f9fa/6c757d?text=波西米亚" alt="波西米亚">
                        <div class="premium-overlay">
                            <i class="fas fa-crown"></i>
                            <span>会员专享</span>
                        </div>
                        <h4>波西米亚</h4>
                        <p>自由浪漫</p>
                    </div>
                    <div class="style-card premium-locked" data-style="luxury">
                        <img src="https://via.placeholder.com/150x200/f8f9fa/6c757d?text=奢华款" alt="奢华款">
                        <div class="premium-overlay">
                            <i class="fas fa-crown"></i>
                            <span>会员专享</span>
                        </div>
                        <h4>奢华款</h4>
                        <p>华丽典雅</p>
                    </div>
                    <div class="style-card premium-locked" data-style="minimalist">
                        <img src="https://via.placeholder.com/150x200/f8f9fa/6c757d?text=极简款" alt="极简款">
                        <div class="premium-overlay">
                            <i class="fas fa-crown"></i>
                            <span>会员专享</span>
                        </div>
                        <h4>极简款</h4>
                        <p>简约纯净</p>
                    </div>
                    <div class="style-card premium-locked" data-style="fairy">
                        <img src="https://via.placeholder.com/150x200/f8f9fa/6c757d?text=仙女款" alt="仙女款">
                        <div class="premium-overlay">
                            <i class="fas fa-crown"></i>
                            <span>会员专享</span>
                        </div>
                        <h4>仙女款</h4>
                        <p>飘逸梦幻</p>
                    </div>
                </div>
            </section>

            <!-- 背景选择区域 -->
            <section class="background-section">
                <div class="section-header">
                    <h2>选择背景场景</h2>
                    <button class="btn-toggle" onclick="toggleBackgroundView()" id="backgroundToggle">
                        <i class="fas fa-th-large"></i>
                        查看更多
                    </button>
                </div>
                
                <!-- 基础背景 -->
                <div class="background-grid basic-backgrounds">
                    <div class="background-card" data-bg="beach">
                        <img src="https://via.placeholder.com/200x120/87ceeb/ffffff?text=海滩" alt="海滩">
                        <h4>海滩</h4>
                    </div>
                    <div class="background-card" data-bg="garden">
                        <img src="https://via.placeholder.com/200x120/90ee90/ffffff?text=花园" alt="花园">
                        <h4>花园</h4>
                    </div>
                    <div class="background-card" data-bg="church">
                        <img src="https://via.placeholder.com/200x120/dda0dd/ffffff?text=教堂" alt="教堂">
                        <h4>教堂</h4>
                    </div>
                    <div class="background-card" data-bg="mountain">
                        <img src="https://via.placeholder.com/200x120/8fbc8f/ffffff?text=山景" alt="山景">
                        <h4>山景</h4>
                    </div>
                </div>
                
                <!-- 高级背景（会员专享） -->
                <div class="background-grid premium-backgrounds" id="premiumBackgrounds" style="display: none;">
                    <div class="background-card premium-locked" data-bg="castle">
                        <img src="https://via.placeholder.com/200x120/d4af37/ffffff?text=城堡" alt="城堡">
                        <div class="premium-overlay">
                            <i class="fas fa-crown"></i>
                            <span>会员专享</span>
                        </div>
                        <h4>古堡</h4>
                    </div>
                    <div class="background-card premium-locked" data-bg="lavender">
                        <img src="https://via.placeholder.com/200x120/9370db/ffffff?text=薰衣草" alt="薰衣草">
                        <div class="premium-overlay">
                            <i class="fas fa-crown"></i>
                            <span>会员专享</span>
                        </div>
                        <h4>薰衣草田</h4>
                    </div>
                    <div class="background-card premium-locked" data-bg="sakura">
                        <img src="https://via.placeholder.com/200x120/ffb6c1/ffffff?text=樱花" alt="樱花">
                        <div class="premium-overlay">
                            <i class="fas fa-crown"></i>
                            <span>会员专享</span>
                        </div>
                        <h4>樱花林</h4>
                    </div>
                    <div class="background-card premium-locked" data-bg="palace">
                        <img src="https://via.placeholder.com/200x120/ffd700/ffffff?text=宫殿" alt="宫殿">
                        <div class="premium-overlay">
                            <i class="fas fa-crown"></i>
                            <span>会员专享</span>
                        </div>
                        <h4>皇宫</h4>
                    </div>
                </div>
            </section>

            <!-- 生成按钮 -->
            <section class="generate-section">
                <button class="btn-generate" onclick="generatePhoto()" disabled id="generateBtn">
                    <i class="fas fa-magic"></i>
                    生成AI婚纱照
                </button>
            </section>
        </main>
    </div>

    <!-- 生成中页面 -->
    <div id="generating" class="screen">
        <div class="generating-content">
            <div class="generating-animation">
                <div class="spinner"></div>
                <i class="fas fa-magic"></i>
            </div>
            <h2>AI正在为您生成婚纱照</h2>
            <p>请稍候，这通常需要30-60秒...</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">0%</div>
        </div>
    </div>

    <!-- 结果页面 -->
    <div id="result" class="screen">
        <header class="result-header">
            <button class="btn-back" onclick="showScreen('main')">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h2>您的AI婚纱照</h2>
            <button class="btn-icon" onclick="sharePhoto()">
                <i class="fas fa-share"></i>
            </button>
        </header>
        
        <div class="result-content">
            <div class="result-image">
                <img id="resultImage" src="https://via.placeholder.com/300x400/f8f9fa/6c757d?text=生成的婚纱照" alt="生成的婚纱照">
            </div>
            
            <div class="result-actions">
                <button class="btn-primary" onclick="saveToGallery()">
                    <i class="fas fa-download"></i>
                    保存到相册
                </button>
                <button class="btn-secondary" onclick="regeneratePhoto()">
                    <i class="fas fa-redo"></i>
                    重新生成
                </button>
            </div>
            
            <div class="result-info">
                <h3>生成信息</h3>
                <div class="info-item">
                    <span class="label">风格：</span>
                    <span class="value" id="selectedStyle">经典款</span>
                </div>
                <div class="info-item">
                    <span class="label">背景：</span>
                    <span class="value" id="selectedBackground">海滩</span>
                </div>
                <div class="info-item">
                    <span class="label">生成时间：</span>
                    <span class="value" id="generateTime">45秒</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 相册页面 -->
    <div id="gallery" class="screen">
        <header class="gallery-header">
            <button class="btn-back" onclick="showScreen('main')">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h2>我的作品</h2>
            <button class="btn-icon" onclick="clearGallery()">
                <i class="fas fa-trash"></i>
            </button>
        </header>
        
        <div class="gallery-content">
            <div class="gallery-grid" id="galleryGrid">
                <!-- 示例照片 -->
                <div class="gallery-item">
                    <img src="https://via.placeholder.com/150x200/f8f9fa/6c757d?text=作品1" alt="作品1">
                    <div class="gallery-overlay">
                        <button class="btn-icon" onclick="viewPhoto(this)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-icon" onclick="deletePhoto(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="gallery-item">
                    <img src="https://via.placeholder.com/150x200/f8f9fa/6c757d?text=作品2" alt="作品2">
                    <div class="gallery-overlay">
                        <button class="btn-icon" onclick="viewPhoto(this)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-icon" onclick="deletePhoto(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="empty-gallery" id="emptyGallery" style="display: none;">
                <i class="fas fa-images"></i>
                <h3>还没有作品</h3>
                <p>开始创建您的第一张AI婚纱照吧</p>
                <button class="btn-primary" onclick="showScreen('main')">
                    <i class="fas fa-plus"></i>
                    开始创作
                </button>
            </div>
        </div>
    </div>

    <!-- 会员订阅页面 -->
    <div id="subscription" class="screen">
        <header class="subscription-header">
            <button class="btn-back" onclick="showScreen('main')">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h2>会员订阅</h2>
            <button class="btn-icon" onclick="restorePurchases()">
                <i class="fas fa-history"></i>
            </button>
        </header>
        
        <div class="subscription-content">
            <!-- 会员状态卡片 -->
            <div class="membership-status" id="membershipStatus">
                <div class="status-free">
                    <div class="status-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="status-info">
                        <h3>免费用户</h3>
                        <p>今日剩余生成次数：<span id="remainingCount">3</span>/3</p>
                    </div>
                    <button class="btn-upgrade" onclick="showSubscriptionPlans()">
                        <i class="fas fa-crown"></i>
                        升级会员
                    </button>
                </div>
                
                <div class="status-premium" style="display: none;">
                    <div class="status-icon premium">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div class="status-info">
                        <h3>尊享会员</h3>
                        <p>有效期至：<span id="expiryDate">2024-07-28</span></p>
                    </div>
                    <button class="btn-manage" onclick="manageMembership()">
                        管理订阅
                    </button>
                </div>
            </div>
            
            <!-- 功能对比 -->
            <div class="feature-comparison">
                <h3>功能对比</h3>
                <div class="comparison-table">
                    <div class="comparison-header">
                        <div class="feature-col">功能</div>
                        <div class="free-col">免费版</div>
                        <div class="premium-col">会员版</div>
                    </div>
                    
                    <div class="comparison-row">
                        <div class="feature-col">
                            <i class="fas fa-magic"></i>
                            每日生成次数
                        </div>
                        <div class="free-col">3次</div>
                        <div class="premium-col">
                            <i class="fas fa-infinity"></i>
                            无限制
                        </div>
                    </div>
                    
                    <div class="comparison-row">
                        <div class="feature-col">
                            <i class="fas fa-image"></i>
                            图片质量
                        </div>
                        <div class="free-col">标准画质</div>
                        <div class="premium-col">超高清画质</div>
                    </div>
                    
                    <div class="comparison-row">
                        <div class="feature-col">
                            <i class="fas fa-palette"></i>
                            风格选择
                        </div>
                        <div class="free-col">4种基础风格</div>
                        <div class="premium-col">20+种高级风格</div>
                    </div>
                    
                    <div class="comparison-row">
                        <div class="feature-col">
                            <i class="fas fa-mountain"></i>
                            背景场景
                        </div>
                        <div class="free-col">4种基础场景</div>
                        <div class="premium-col">50+种精美场景</div>
                    </div>
                    
                    <div class="comparison-row">
                        <div class="feature-col">
                            <i class="fas fa-copyright"></i>
                            水印
                        </div>
                        <div class="free-col">
                            <i class="fas fa-times text-error"></i>
                            有水印
                        </div>
                        <div class="premium-col">
                            <i class="fas fa-check text-success"></i>
                            无水印
                        </div>
                    </div>
                    
                    <div class="comparison-row">
                        <div class="feature-col">
                            <i class="fas fa-layer-group"></i>
                            批量生成
                        </div>
                        <div class="free-col">
                            <i class="fas fa-times text-error"></i>
                            不支持
                        </div>
                        <div class="premium-col">
                            <i class="fas fa-check text-success"></i>
                            支持
                        </div>
                    </div>
                    
                    <div class="comparison-row">
                        <div class="feature-col">
                            <i class="fas fa-cloud"></i>
                            云端同步
                        </div>
                        <div class="free-col">
                            <i class="fas fa-times text-error"></i>
                            不支持
                        </div>
                        <div class="premium-col">
                            <i class="fas fa-check text-success"></i>
                            支持
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 订阅方案 -->
            <div class="subscription-plans" id="subscriptionPlans" style="display: none;">
                <h3>选择订阅方案</h3>
                <div class="plans-grid">
                    <div class="plan-card" data-plan="monthly">
                        <div class="plan-header">
                            <h4>月度会员</h4>
                            <div class="plan-price">
                                <span class="currency">¥</span>
                                <span class="amount">29.9</span>
                                <span class="period">/月</span>
                            </div>
                        </div>
                        <div class="plan-features">
                            <div class="feature-item">
                                <i class="fas fa-check"></i>
                                所有会员功能
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-check"></i>
                                随时取消
                            </div>
                        </div>
                        <button class="btn-plan" onclick="selectPlan('monthly')">选择方案</button>
                    </div>
                    
                    <div class="plan-card popular" data-plan="quarterly">
                        <div class="plan-badge">最受欢迎</div>
                        <div class="plan-header">
                            <h4>季度会员</h4>
                            <div class="plan-price">
                                <span class="currency">¥</span>
                                <span class="amount">79.9</span>
                                <span class="period">/季</span>
                            </div>
                            <div class="plan-save">节省11%</div>
                        </div>
                        <div class="plan-features">
                            <div class="feature-item">
                                <i class="fas fa-check"></i>
                                所有会员功能
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-check"></i>
                                更优惠的价格
                            </div>
                        </div>
                        <button class="btn-plan primary" onclick="selectPlan('quarterly')">选择方案</button>
                    </div>
                    
                    <div class="plan-card" data-plan="yearly">
                        <div class="plan-header">
                            <h4>年度会员</h4>
                            <div class="plan-price">
                                <span class="currency">¥</span>
                                <span class="amount">299.9</span>
                                <span class="period">/年</span>
                            </div>
                            <div class="plan-save">节省17%</div>
                        </div>
                        <div class="plan-features">
                            <div class="feature-item">
                                <i class="fas fa-check"></i>
                                所有会员功能
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-check"></i>
                                最大优惠
                            </div>
                        </div>
                        <button class="btn-plan" onclick="selectPlan('yearly')">选择方案</button>
                    </div>
                </div>
                
                <div class="subscription-footer">
                    <p class="subscription-note">
                        <i class="fas fa-info-circle"></i>
                        订阅将自动续费，可随时在设置中取消
                    </p>
                    <div class="subscription-links">
                        <a href="#" onclick="showTerms()">服务条款</a>
                        <span>·</span>
                        <a href="#" onclick="showPrivacy()">隐私政策</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置页面 -->
    <div id="settings" class="screen">
        <header class="settings-header">
            <button class="btn-back" onclick="showScreen('main')">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h2>设置</h2>
        </header>
        
        <div class="settings-content">
            <!-- 会员状态 -->
            <div class="settings-section">
                <h3>会员服务</h3>
                <div class="setting-item clickable" onclick="showScreen('subscription')">
                    <div class="setting-info">
                        <i class="fas fa-crown"></i>
                        <div>
                            <h4>会员订阅</h4>
                            <p id="membershipStatusText">免费用户 - 今日剩余3次生成</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
            
            <div class="settings-section">
                <h3>权限设置</h3>
                <div class="setting-item">
                    <div class="setting-info">
                        <i class="fas fa-camera"></i>
                        <div>
                            <h4>相机权限</h4>
                            <p>允许应用访问相机拍摄照片</p>
                        </div>
                    </div>
                    <label class="toggle">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                </div>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <i class="fas fa-images"></i>
                        <div>
                            <h4>相册权限</h4>
                            <p>允许应用访问和保存照片到相册</p>
                        </div>
                    </div>
                    <label class="toggle">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
            
            <div class="settings-section">
                <h3>生成设置</h3>
                <div class="setting-item">
                    <div class="setting-info">
                        <i class="fas fa-image"></i>
                        <div>
                            <h4>图片质量</h4>
                            <p>选择生成图片的质量</p>
                        </div>
                    </div>
                    <select class="setting-select">
                        <option value="standard">标准</option>
                        <option value="high" selected>高清</option>
                        <option value="ultra">超高清</option>
                    </select>
                </div>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <i class="fas fa-clock"></i>
                        <div>
                            <h4>自动保存</h4>
                            <p>生成完成后自动保存到相册</p>
                        </div>
                    </div>
                    <label class="toggle">
                        <input type="checkbox">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
            
            <div class="settings-section">
                <h3>关于</h3>
                <div class="setting-item clickable" onclick="showAbout()">
                    <div class="setting-info">
                        <i class="fas fa-info-circle"></i>
                        <div>
                            <h4>关于应用</h4>
                            <p>版本信息和使用条款</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right"></i>
                </div>
                
                <div class="setting-item clickable" onclick="showPrivacy()">
                    <div class="setting-info">
                        <i class="fas fa-shield-alt"></i>
                        <div>
                            <h4>隐私政策</h4>
                            <p>了解我们如何保护您的隐私</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- 权限请求弹窗 -->
    <div id="permissionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>需要权限</h3>
            </div>
            <div class="modal-body">
                <div class="permission-icon">
                    <i class="fas fa-camera"></i>
                </div>
                <p>为了让您能够上传照片和保存生成的婚纱照，我们需要访问您的相机和相册权限。</p>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeModal()">稍后</button>
                <button class="btn-primary" onclick="requestPermissions()">允许</button>
            </div>
        </div>
    </div>

    <!-- 订阅确认弹窗 -->
    <div id="subscriptionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>确认订阅</h3>
            </div>
            <div class="modal-body">
                <div class="subscription-summary">
                    <div class="plan-info">
                        <div class="plan-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="plan-details">
                            <h4 id="selectedPlanName">季度会员</h4>
                            <p id="selectedPlanPrice">¥79.9/季</p>
                        </div>
                    </div>
                    
                    <div class="subscription-benefits">
                        <h5>您将获得：</h5>
                        <ul>
                            <li><i class="fas fa-check"></i> 无限生成次数</li>
                            <li><i class="fas fa-check"></i> 超高清画质输出</li>
                            <li><i class="fas fa-check"></i> 20+种高级风格</li>
                            <li><i class="fas fa-check"></i> 50+种精美背景</li>
                            <li><i class="fas fa-check"></i> 无水印输出</li>
                            <li><i class="fas fa-check"></i> 批量生成功能</li>
                        </ul>
                    </div>
                    
                    <div class="payment-info">
                        <p class="auto-renew">
                            <i class="fas fa-info-circle"></i>
                            订阅将自动续费，可随时取消
                        </p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeSubscriptionModal()">取消</button>
                <button class="btn-primary" onclick="confirmSubscription()">
                    <i class="fas fa-credit-card"></i>
                    确认订阅
                </button>
            </div>
        </div>
    </div>

    <!-- 升级提示弹窗 -->
    <div id="upgradeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>需要升级会员</h3>
            </div>
            <div class="modal-body">
                <div class="upgrade-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <p id="upgradeMessage">此功能需要会员权限，升级会员即可解锁所有高级功能。</p>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeUpgradeModal()">稍后</button>
                <button class="btn-primary" onclick="goToSubscription()">
                    <i class="fas fa-crown"></i>
                    立即升级
                </button>
            </div>
        </div>
    </div>

    <!-- 登录提示弹窗 -->
    <div id="loginPromptModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>登录账户</h3>
            </div>
            <div class="modal-body">
                <div class="login-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <h4>同步您的作品到云端</h4>
                <p>登录后可以在多个设备间同步您的婚纱照作品，永不丢失珍贵回忆。</p>
                
                <div class="login-benefits">
                    <div class="benefit-item">
                        <i class="fas fa-check"></i>
                        <span>跨设备同步作品</span>
                    </div>
                    <div class="benefit-item">
                        <i class="fas fa-check"></i>
                        <span>云端安全备份</span>
                    </div>
                    <div class="benefit-item">
                        <i class="fas fa-check"></i>
                        <span>个性化推荐</span>
                    </div>
                </div>
                
                <div class="login-note">
                    <i class="fas fa-info-circle"></i>
                    <span>您也可以继续使用本地模式，随时可以登录</span>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeLoginPrompt()">继续本地使用</button>
                <button class="btn-primary" onclick="showLoginOptions()">
                    <i class="fas fa-sign-in-alt"></i>
                    立即登录
                </button>
            </div>
        </div>
    </div>

    <!-- 登录方式选择弹窗 -->
    <div id="loginOptionsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>选择登录方式</h3>
            </div>
            <div class="modal-body">
                <div class="login-options">
                    <button class="login-option" onclick="loginWithApple()">
                        <i class="fab fa-apple"></i>
                        <span>使用 Apple ID 登录</span>
                        <div class="option-badge">推荐</div>
                    </button>
                    
                    <button class="login-option" onclick="loginWithWechat()">
                        <i class="fab fa-weixin"></i>
                        <span>使用微信登录</span>
                    </button>
                    
                    <button class="login-option" onclick="loginWithPhone()">
                        <i class="fas fa-mobile-alt"></i>
                        <span>使用手机号登录</span>
                    </button>
                </div>
                
                <div class="login-terms">
                    <p>登录即表示您同意我们的 
                        <a href="#" onclick="showTerms()">服务条款</a> 和 
                        <a href="#" onclick="showPrivacy()">隐私政策</a>
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeLoginOptions()">取消</button>
            </div>
        </div>
    </div>

    <!-- 云端同步状态弹窗 -->
    <div id="cloudSyncModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>云端同步</h3>
            </div>
            <div class="modal-body">
                <div class="sync-status-display">
                    <div class="sync-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <h4>同步状态：已同步</h4>
                    <p>您的作品已安全保存到云端</p>
                    
                    <div class="sync-info">
                        <div class="info-row">
                            <span class="label">本地作品：</span>
                            <span class="value" id="localPhotoCount">5张</span>
                        </div>
                        <div class="info-row">
                            <span class="label">云端作品：</span>
                            <span class="value" id="cloudPhotoCount">5张</span>
                        </div>
                        <div class="info-row">
                            <span class="label">最后同步：</span>
                            <span class="value" id="lastSyncTime">刚刚</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeCloudSyncModal()">关闭</button>
                <button class="btn-primary" onclick="forceSyncNow()">
                    <i class="fas fa-sync"></i>
                    立即同步
                </button>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="toast" class="toast"></div>

    <script src="script.js"></script>
</body>
</html>