/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow-x: hidden;
}

/* 屏幕管理 */
.screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: #fff;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
    z-index: 1000;
    overflow-y: auto;
}

.screen.active {
    transform: translateX(0);
}

/* 启动页面 */
#splash-screen {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.splash-content {
    text-align: center;
    padding: 2rem;
}

.logo {
    margin-bottom: 2rem;
}

.logo i {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
    color: #ff6b9d;
}

.logo h1 {
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 0.5rem;
}

.tagline {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 3rem;
}

.splash-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 280px;
    margin: 0 auto;
}

/* 按钮样式 */
.btn-primary {
    background: linear-gradient(45deg, #ff6b9d, #ff8e9b);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 157, 0.4);
}

.btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

.btn-icon {
    background: none;
    border: none;
    color: #667eea;
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #5a67d8;
}

.btn-back {
    background: none;
    border: none;
    color: #667eea;
    font-size: 1.2rem;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-back:hover {
    color: #5a67d8;
}

/* 引导页面 */
.onboarding-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 2rem;
}

.onboarding-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.progress-dots {
    display: flex;
    gap: 0.5rem;
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #e2e8f0;
    transition: background 0.3s ease;
}

.dot.active {
    background: #667eea;
}

.onboarding-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.onboarding-step {
    display: none;
    max-width: 300px;
}

.onboarding-step.active {
    display: block;
}

.step-icon {
    margin-bottom: 2rem;
}

.step-icon i {
    font-size: 4rem;
    color: #667eea;
}

.onboarding-step h2 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: #2d3748;
}

.onboarding-step p {
    font-size: 1rem;
    color: #718096;
    line-height: 1.6;
}

.onboarding-footer {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
}

.onboarding-footer .btn-secondary {
    color: #667eea;
    border-color: #667eea;
}

/* 主界面 */
.main-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.main-header h1 {
    font-size: 1.5rem;
    color: #2d3748;
    font-weight: 600;
}

.header-right {
    display: flex;
    gap: 0.5rem;
}

.main-content {
    padding: 2rem 1.5rem;
    max-width: 600px;
    margin: 0 auto;
}

/* 上传区域 */
.upload-section {
    margin-bottom: 3rem;
}

.upload-area {
    border: 2px dashed #cbd5e0;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.upload-placeholder i {
    font-size: 3rem;
    color: #a0aec0;
    margin-bottom: 1rem;
}

.upload-placeholder h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: #2d3748;
}

.upload-placeholder p {
    color: #718096;
    margin-bottom: 1.5rem;
}

.btn-upload {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-upload:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.uploaded-photo {
    text-align: center;
}

.uploaded-photo img {
    max-width: 200px;
    max-height: 250px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

.photo-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* 风格选择 */
.style-section, .background-section {
    margin-bottom: 3rem;
}

.style-section h2, .background-section h2 {
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
    color: #2d3748;
}

.style-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.style-card {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.style-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.style-card.selected {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.style-card img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 0.8rem;
}

.style-card h4 {
    font-size: 1rem;
    margin-bottom: 0.3rem;
    color: #2d3748;
}

.style-card p {
    font-size: 0.9rem;
    color: #718096;
}

/* 背景选择 */
.background-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.background-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.background-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.background-card.selected {
    border-color: #667eea;
}

.background-card img {
    width: 100%;
    height: 100px;
    object-fit: cover;
}

.background-card h4 {
    padding: 0.8rem;
    text-align: center;
    font-size: 1rem;
    color: #2d3748;
}

/* 生成按钮 */
.generate-section {
    text-align: center;
    margin-top: 2rem;
}

.btn-generate {
    background: linear-gradient(45deg, #ff6b9d, #ff8e9b);
    color: white;
    border: none;
    padding: 1.2rem 2.5rem;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
}

.btn-generate:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 157, 0.4);
}

.btn-generate:disabled {
    background: #e2e8f0;
    color: #a0aec0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 生成中页面 */
.generating-content {
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 2rem;
}

.generating-animation {
    position: relative;
    margin-bottom: 2rem;
}

.spinner {
    width: 80px;
    height: 80px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.generating-animation i {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    color: #667eea;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.generating-content h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #2d3748;
}

.generating-content p {
    color: #718096;
    margin-bottom: 2rem;
}

.progress-bar {
    width: 100%;
    max-width: 300px;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 1rem;
    color: #667eea;
    font-weight: 600;
}

/* 结果页面 */
.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.result-header h2 {
    font-size: 1.3rem;
    color: #2d3748;
}

.result-content {
    padding: 2rem 1.5rem;
    text-align: center;
    max-width: 500px;
    margin: 0 auto;
}

.result-image {
    margin-bottom: 2rem;
}

.result-image img {
    max-width: 100%;
    max-height: 400px;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.result-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    justify-content: center;
}

.result-info {
    background: #f7fafc;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: left;
}

.result-info h3 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #2d3748;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.8rem;
}

.info-item:last-child {
    margin-bottom: 0;
}

.label {
    color: #718096;
    font-weight: 500;
}

.value {
    color: #2d3748;
    font-weight: 600;
}

/* 相册页面 */
.gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.gallery-header h2 {
    font-size: 1.3rem;
    color: #2d3748;
}

.gallery-content {
    padding: 2rem 1.5rem;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.gallery-item {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-2px);
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay .btn-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.gallery-overlay .btn-icon:hover {
    background: rgba(255, 255, 255, 0.3);
}

.empty-gallery {
    text-align: center;
    padding: 4rem 2rem;
    color: #718096;
}

.empty-gallery i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #cbd5e0;
}

.empty-gallery h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #4a5568;
}

.empty-gallery p {
    margin-bottom: 2rem;
}

/* 会员状态提示条 */
.membership-banner {
    background: linear-gradient(45deg, #ff6b9d, #ff8e9b);
    color: white;
    padding: 0.8rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.banner-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.banner-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.btn-banner {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.btn-banner:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 会员订阅页面 */
.subscription-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.subscription-header h2 {
    font-size: 1.3rem;
    color: #2d3748;
}

.subscription-content {
    padding: 2rem 1.5rem;
    max-width: 600px;
    margin: 0 auto;
}

/* 会员状态卡片 */
.membership-status {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.status-free, .status-premium {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #718096;
}

.status-icon.premium {
    background: linear-gradient(45deg, #ff6b9d, #ff8e9b);
    color: white;
}

.status-info {
    flex: 1;
}

.status-info h3 {
    font-size: 1.2rem;
    color: #2d3748;
    margin-bottom: 0.3rem;
}

.status-info p {
    color: #718096;
    font-size: 0.9rem;
}

.btn-upgrade, .btn-manage {
    background: linear-gradient(45deg, #ff6b9d, #ff8e9b);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-upgrade:hover, .btn-manage:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
}

/* 功能对比表 */
.feature-comparison {
    margin-bottom: 2rem;
}

.feature-comparison h3 {
    font-size: 1.2rem;
    color: #2d3748;
    margin-bottom: 1rem;
}

.comparison-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.comparison-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    background: #f7fafc;
    padding: 1rem;
    font-weight: 600;
    color: #2d3748;
    border-bottom: 1px solid #e2e8f0;
}

.comparison-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    align-items: center;
}

.comparison-row:last-child {
    border-bottom: none;
}

.feature-col {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    color: #2d3748;
}

.feature-col i {
    color: #667eea;
    width: 16px;
}

.free-col, .premium-col {
    text-align: center;
    font-size: 0.9rem;
}

.free-col {
    color: #718096;
}

.premium-col {
    color: #2d3748;
    font-weight: 500;
}

.text-success {
    color: #48bb78;
}

.text-error {
    color: #f56565;
}

/* 订阅方案 */
.subscription-plans {
    margin-top: 2rem;
}

.subscription-plans h3 {
    font-size: 1.2rem;
    color: #2d3748;
    margin-bottom: 1.5rem;
    text-align: center;
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.plan-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.plan-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.plan-card.popular {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.plan-badge {
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    background: #667eea;
    color: white;
    padding: 0.3rem 1rem;
    border-radius: 0 0 8px 8px;
    font-size: 0.8rem;
    font-weight: 600;
}

.plan-header {
    margin-bottom: 1.5rem;
}

.plan-header h4 {
    font-size: 1.1rem;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.plan-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.2rem;
}

.currency {
    font-size: 1rem;
    color: #718096;
}

.amount {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
}

.period {
    font-size: 1rem;
    color: #718096;
}

.plan-save {
    background: #48bb78;
    color: white;
    padding: 0.2rem 0.8rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-top: 0.5rem;
    display: inline-block;
}

.plan-features {
    margin-bottom: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #2d3748;
}

.feature-item i {
    color: #48bb78;
    font-size: 0.8rem;
}

.btn-plan {
    width: 100%;
    background: #667eea;
    color: white;
    border: none;
    padding: 0.8rem;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-plan:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.btn-plan.primary {
    background: linear-gradient(45deg, #ff6b9d, #ff8e9b);
}

.btn-plan.primary:hover {
    background: linear-gradient(45deg, #ff5a8a, #ff7d88);
}

.subscription-footer {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

.subscription-note {
    color: #718096;
    font-size: 0.8rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.subscription-links {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.8rem;
}

.subscription-links a {
    color: #667eea;
    text-decoration: none;
}

.subscription-links a:hover {
    text-decoration: underline;
}

/* 风格和背景选择优化 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    font-size: 1.4rem;
    color: #2d3748;
}

.btn-toggle {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.btn-toggle:hover {
    background: #5a67d8;
}

/* 高级功能锁定状态 */
.premium-locked {
    position: relative;
    opacity: 0.7;
}

.premium-locked::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    z-index: 1;
}

.premium-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, #ff6b9d, #ff8e9b);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    box-shadow: 0 2px 10px rgba(255, 107, 157, 0.3);
}

.premium-locked:hover {
    cursor: pointer;
    transform: translateY(-1px);
}

/* 订阅确认弹窗 */
.subscription-summary {
    text-align: center;
}

.plan-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 8px;
}

.plan-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff6b9d, #ff8e9b);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.plan-details h4 {
    font-size: 1.2rem;
    color: #2d3748;
    margin-bottom: 0.2rem;
}

.plan-details p {
    color: #718096;
    font-size: 0.9rem;
}

.subscription-benefits {
    text-align: left;
    margin-bottom: 1.5rem;
}

.subscription-benefits h5 {
    font-size: 1rem;
    color: #2d3748;
    margin-bottom: 0.8rem;
}

.subscription-benefits ul {
    list-style: none;
    padding: 0;
}

.subscription-benefits li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #2d3748;
}

.subscription-benefits li i {
    color: #48bb78;
    font-size: 0.8rem;
}

.payment-info {
    padding: 1rem;
    background: #f7fafc;
    border-radius: 8px;
}

.auto-renew {
    color: #718096;
    font-size: 0.8rem;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

/* 用户菜单 */
.user-menu {
    position: absolute;
    top: 60px;
    right: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 280px;
    overflow: hidden;
}

.user-menu-content {
    padding: 0;
}

.user-status {
    padding: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.user-info h4 {
    font-size: 1.1rem;
    margin-bottom: 0.2rem;
    font-weight: 600;
}

.user-info p {
    font-size: 0.9rem;
    opacity: 0.9;
    margin: 0;
}

.menu-divider {
    height: 1px;
    background: #e2e8f0;
}

.menu-items {
    padding: 0.5rem 0;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: background 0.3s ease;
    position: relative;
}

.menu-item:hover {
    background: #f7fafc;
}

.menu-item i {
    width: 20px;
    color: #667eea;
    font-size: 1.1rem;
}

.menu-item span {
    flex: 1;
    color: #2d3748;
    font-weight: 500;
}

.menu-badge {
    background: #ff6b9d;
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
}

.sync-status {
    display: flex;
    align-items: center;
}

.sync-status i {
    color: #48bb78;
    font-size: 1rem;
}

/* 登录相关弹窗 */
.login-icon, .sync-icon {
    text-align: center;
    margin-bottom: 1rem;
}

.login-icon i, .sync-icon i {
    font-size: 3rem;
    color: #667eea;
}

.login-benefits {
    margin: 1.5rem 0;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 0.8rem;
    font-size: 0.9rem;
    color: #2d3748;
}

.benefit-item i {
    color: #48bb78;
    font-size: 0.9rem;
    width: 16px;
}

.login-note {
    background: #f7fafc;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #718096;
}

.login-note i {
    color: #667eea;
    font-size: 0.9rem;
}

/* 登录选项 */
.login-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.login-option {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    width: 100%;
    text-align: left;
}

.login-option:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.login-option i {
    font-size: 1.5rem;
    color: #2d3748;
    width: 24px;
}

.login-option span {
    flex: 1;
    font-size: 1rem;
    font-weight: 500;
    color: #2d3748;
}

.option-badge {
    background: #48bb78;
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
}

.login-terms {
    text-align: center;
    font-size: 0.8rem;
    color: #718096;
    line-height: 1.5;
}

.login-terms a {
    color: #667eea;
    text-decoration: none;
}

.login-terms a:hover {
    text-decoration: underline;
}

/* 云端同步状态 */
.sync-status-display {
    text-align: center;
}

.sync-status-display h4 {
    font-size: 1.2rem;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.sync-status-display p {
    color: #718096;
    margin-bottom: 1.5rem;
}

.sync-info {
    background: #f7fafc;
    border-radius: 8px;
    padding: 1rem;
    text-align: left;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.8rem;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-row .label {
    color: #718096;
    font-size: 0.9rem;
}

.info-row .value {
    color: #2d3748;
    font-weight: 600;
    font-size: 0.9rem;
}

/* 升级提示弹窗 */
.upgrade-icon {
    text-align: center;
    margin-bottom: 1rem;
}

.upgrade-icon i {
    font-size: 3rem;
    color: #ff6b9d;
}

/* 设置页面 */
.settings-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.settings-header h2 {
    font-size: 1.3rem;
    color: #2d3748;
}

.settings-content {
    padding: 2rem 1.5rem;
}

.settings-section {
    margin-bottom: 2rem;
}

.settings-section h3 {
    font-size: 1.2rem;
    color: #2d3748;
    margin-bottom: 1rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: white;
    border-radius: 12px;
    margin-bottom: 0.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.setting-item.clickable {
    cursor: pointer;
    transition: background 0.3s ease;
}

.setting-item.clickable:hover {
    background: #f7fafc;
}

.setting-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.setting-info i {
    font-size: 1.2rem;
    color: #667eea;
    width: 20px;
    text-align: center;
}

.setting-info h4 {
    font-size: 1rem;
    color: #2d3748;
    margin-bottom: 0.2rem;
}

.setting-info p {
    font-size: 0.9rem;
    color: #718096;
}

.setting-select {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    color: #2d3748;
    font-size: 0.9rem;
}

/* 切换开关 */
.toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #667eea;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 12px;
    max-width: 400px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: 1.5rem 1.5rem 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    font-size: 1.3rem;
    color: #2d3748;
}

.modal-body {
    padding: 1.5rem;
    text-align: center;
}

.permission-icon {
    margin-bottom: 1rem;
}

.permission-icon i {
    font-size: 3rem;
    color: #667eea;
}

.modal-body p {
    color: #718096;
    line-height: 1.6;
}

.modal-footer {
    padding: 1rem 1.5rem 1.5rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Toast 提示 */
.toast {
    position: fixed;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    background: #2d3748;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    font-size: 0.9rem;
    z-index: 3000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.toast.show {
    opacity: 1;
    visibility: visible;
}

.toast.success {
    background: #48bb78;
}

.toast.error {
    background: #f56565;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .main-content {
        padding: 1.5rem 1rem;
    }
    
    .style-grid, .background-grid {
        grid-template-columns: 1fr;
    }
    
    .result-actions {
        flex-direction: column;
    }
    
    .gallery-grid {
        grid-template-columns: 1fr;
    }
    
    .onboarding-footer {
        flex-direction: column;
    }
    
    .splash-buttons {
        width: 100%;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}