# AI婚纱照生成服务 - 完整使用指南

## 概述

本项目提供完整的AI婚纱照生成解决方案，包含安全的API密钥管理、人像检测、图片分析和高质量婚纱照生成功能。

## 🚀 最新优化内容

### 1. 🔐 安全的API密钥管理
- ✅ 避免硬编码API密钥，支持环境变量和配置文件
- ✅ 自动脱敏显示，保护敏感信息
- ✅ 多种配置方式：环境变量 > 配置文件 > 调试模式
- ✅ 完整的配置验证和错误处理

### 2. 👤 智能人像检测
- ✅ 自动检测图片是否包含人像
- ✅ 评估人像质量和适用性
- ✅ 过滤非人像图片，确保生成质量
- ✅ 详细的检测报告和改进建议

### 3. 🎨 完整的婚纱照生成流程
- ✅ 从输入验证到最终输出的完整流程
- ✅ 专业级的图片分析prompt
- ✅ 多风格生成支持
- ✅ 详细的处理时间和结果统计

### 4. 🛡️ 增强的安全性和稳定性
- ✅ 完善的错误处理和异常管理
- ✅ 文件验证和大小限制
- ✅ 网络请求重试和超时控制
- ✅ 详细的日志记录（不包含敏感信息）

## 🚀 快速开始

### 1. 🔑 安全配置API密钥

**方法一：环境变量（推荐）**
```bash
export LAOZHANG_AI_API_KEY="your_actual_api_key_here"
```

**方法二：配置文件**
1. 复制模板文件：
   ```bash
   cp assets/config/api_config.txt.template assets/config/api_config.txt
   ```
2. 编辑 `assets/config/api_config.txt`：
   ```
   LAOZHANG_AI_API_KEY=your_actual_api_key_here
   ```

**方法三：代码设置（仅用于测试）**
```dart
import 'package:your_app/core/config/secure_config.dart';

await SecureConfig.initialize();
SecureConfig.setApiKey('your_actual_api_key_here'); // 仅用于测试
```

### 2. 📱 基本使用

```dart
import 'package:your_app/core/services/wedding_photo_generation_service.dart';

final service = WeddingPhotoGenerationService();

// 初始化配置
await SecureConfig.initialize();

// 完整的婚纱照生成流程
final result = await service.generateWeddingPhoto(
  inputImages: [File('path/to/portrait.jpg')],
  style: 'romantic',
  size: '1024x1024',
  quality: 'hd',
);

if (result.success) {
  print('✅ 生成成功！');
  print('生成图片: ${result.generatedImageUrls}');
  print('处理时间: ${result.processingTimeInSeconds}秒');

  // 人像检测结果
  for (final detection in result.portraitDetectionResults) {
    print('人像检测: ${detection.message}');
  }
} else {
  print('❌ 生成失败: ${result.message}');
}
```

### 3. 🎯 高级功能

```dart
// 快速生成（单张图片）
final result = await service.quickGenerate(File('portrait.jpg'));

// 高级生成（多风格）
final result = await service.premiumGenerate([
  File('portrait1.jpg'),
  File('portrait2.jpg'),
]);

// 仅进行人像检测
final detectionResults = await service.detectPortraitsOnly([
  File('image1.jpg'),
  File('image2.jpg'),
]);

for (final detection in detectionResults) {
  print('人像检测: ${detection.hasPortrait}');
  print('置信度: ${detection.confidence}');
  print('适合婚纱照: ${detection.isSuitableForWedding}');
}

// 自定义参数生成
final result = await service.generateWeddingPhoto(
  inputImages: [File('portrait.jpg')],
  style: 'classic',
  size: '1024x1024',
  quality: 'hd',
  generateMultipleStyles: true,
  skipPortraitDetection: false, // 是否跳过人像检测
);

// 检查服务状态
final status = await service.getServiceStatus();
print('服务状态: ${status['status']}');
print('API密钥有效: ${status['apiKeyValid']}');
```

## API调用示例

基于您提供的API调用示例，服务现在使用以下配置：

```javascript
// 参考的API调用方式
const openai = new OpenAI({
  apiKey: "YOUR_API_KEY",
  baseURL: "https://api.laozhang.ai/v1"
});

const completion = await openai.chat.completions.create({
  model: "gpt-4o",
  messages: [
    {"role": "user", "content": "write a haiku about ai"}
  ]
});
```

对应的Dart实现：

```dart
final requestData = {
  'model': 'gpt-4o',
  'messages': [
    {
      'role': 'user',
      'content': [
        {'type': 'text', 'text': prompt},
        {'type': 'image_url', 'image_url': {'url': 'data:image/jpeg;base64,$base64Image'}}
      ]
    }
  ],
  'max_tokens': 1200,
  'temperature': 0.7,
};
```

## 婚纱照风格预设

服务提供多种预设风格：

- **经典优雅** (`classic`): 传统经典的婚纱照风格
- **浪漫梦幻** (`romantic`): 充满浪漫气息的梦幻婚纱照
- **现代时尚** (`modern`): 现代简约的时尚婚纱照风格
- **复古怀旧** (`vintage`): 复古风格的怀旧婚纱照
- **自然清新** (`natural`): 自然清新的户外婚纱照风格

## 场景预设

- **教堂** (`church`): 庄严神圣的教堂场景
- **花园** (`garden`): 美丽的花园场景
- **海滩** (`beach`): 浪漫的海滩场景
- **城堡** (`castle`): 童话般的城堡场景
- **摄影棚** (`studio`): 专业的室内摄影棚

## 配置参数

### 图片限制
- 单张图片最大: 10MB
- 总图片大小: 50MB
- 最大图片数量: 5张
- 支持格式: jpg, jpeg, png, webp, gif

### 生成参数
- 尺寸选项: 512x512, 1024x1024, 1792x1024
- 质量选项: standard, hd
- 风格选项: vivid, natural

### 超时设置
- 连接超时: 30秒
- 接收超时: 120秒
- 发送超时: 60秒

## 错误处理

服务提供详细的错误处理：

```dart
try {
  final result = await aiService.generateWeddingPhotos(images);
  // 处理成功结果
} on DioException catch (e) {
  // 网络错误
  print('网络错误: ${e.message}');
} catch (e) {
  // 其他错误
  print('生成失败: $e');
}
```

## 生成结果数据结构

```dart
class GenerationResult {
  final bool success;                    // 是否成功
  final String analysis;                 // AI分析结果
  final String generatedImageUrl;        // 主要生成的图片URL
  final List<String> additionalImageUrls; // 额外生成的图片URLs
  final String message;                  // 结果消息
  final DateTime generationTime;         // 生成时间
  final String style;                    // 使用的风格
  final String size;                     // 图片尺寸
  final String quality;                  // 图片质量
  
  // 便捷方法
  List<String> get allImageUrls;         // 所有图片URLs
  bool get hasMultipleImages;            // 是否有多张图片
  int get imageCount;                    // 图片总数
}
```

## 示例代码

完整的使用示例请参考 `lib/examples/ai_generation_example.dart` 文件。

## 注意事项

1. **API密钥**: 请确保在 `AIConfig` 中设置正确的API密钥
2. **网络连接**: 确保设备有稳定的网络连接
3. **图片格式**: 仅支持常见的图片格式
4. **文件大小**: 注意图片文件大小限制
5. **API限制**: 遵守API提供商的使用限制和频率限制

## 调试

启用调试模式查看详细日志：

```dart
// 在debug模式下会自动启用详细日志
if (kDebugMode) {
  print('调试信息: ${AIConfig.getDebugInfo()}');
}
```

## 更新日志

- ✅ 更新API基础URL为laozhang.ai
- ✅ 优化婚纱照生成prompt
- ✅ 添加多风格生成支持
- ✅ 增强错误处理和日志记录
- ✅ 创建统一配置管理
- ✅ 添加文件验证功能
- ✅ 提供完整的使用示例

## 技术支持

如有问题，请检查：
1. API密钥是否正确设置
2. 网络连接是否正常
3. 图片文件是否符合要求
4. 查看控制台日志获取详细错误信息
